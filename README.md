# Overview introduction
This project is an Australian - focused web application inspired by "Smzdm", tailored to meet the unique shopping needs of Australians. Our product positioning is to be the go - to platform for smart shoppers across the continent.

## wp-system
### Brief Introduction
The application offers a range of features. Firstly, it aggregates deals from various Australian retailers, both online and in - store, covering categories like electronics, fashion, homeware, and groceries. Users can easily browse through these offers, compare prices, and find the best value for their money. Secondly, it has a community - driven review section. Shoppers can share their experiences, leave product reviews, and get advice from fellow Australians. Thirdly, personalized recommendations are provided based on users' browsing and purchase history, ensuring they never miss out on relevant and attractive deals.


## Backend-system
### Brief Introduction
This is a powerful AI-Agent framework based backend, which leverages LLM and a crawler to retrieve hot deals information from popular e-commerce websites. It AI-generates deal content or article context and then publishes the deal or article to an external WordPress website using an API. All information crawled from the website is stored locally in both RDBMS and vector databases, allowing us to leverage this data for RAG (Retrieval-Augmented Generation) tasks to generate content for deals and interact with end users.
[Click to view the README](./ai-backend-system/src/README.md)

# Business Architecture
[Click to view the design document](./docs/design/overview/4A-01-Business-Architecture.md)
# Information Architecture
[Click to view the design document](./docs/design/overview/4A-02-Information-Architecture.md)
# Application Architecture
[Click to view the design document](./docs/design/overview/4A-03-Application-Architecture.md)
# Technical Architecture
[Click to view the design document](./docs/design/overview/4A-04-Technical-Architecture.md)
