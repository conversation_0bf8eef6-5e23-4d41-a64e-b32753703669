# 信息架构设计文档（更新版）

## 1. 识别和定义信息资产

### 1.1 数据实体更新

基于后续 AA 和 TA 设计的需求，商品域和用户域将基于 WordPress开发，内容域则服务于AI 驱动的后端系统。数据实体更新如下：

#### 商品域（基于 WordPress）
- **Product（商品）**：复用 wp_posts 表，通过 post_type = 'product' 区分，扩展属性存储在 wp_postmeta 中。
- **Deal（优惠信息）**：复用 wp_posts 表，通过 post_type = 'deal' 区分，扩展属性存储在 wp_postmeta 中。
- **Review Article（评测文章）**：复用 wp_posts 表，通过 post_type = 'review_article' 区分，扩展属性存储在 wp_postmeta 中。
- **Category（分类）**：复用 wp_terms 和 wp_term_taxonomy 表，通过 taxonomy = 'category' 实现。
- **Tag（标签）**：复用 wp_terms 和 wp_term_taxonomy 表，通过 taxonomy = 'post_tag' 或自定义 taxonomy 实现（需插件支持）。

#### 用户域（基于 WordPress）
- **User（用户）**：复用 wp_users 表，扩展属性存储在 wp_usermeta 中。
- **Comment（评论）**：复用 wp_comments 表，关联到 wp_posts 中的 Deal 或 Review Article。
- **Follow Relationship（关注关系）**：通过插件（如 BuddyPress 或自定义插件）扩展实现，存储在自定义表（如 wp_follow_relationship）。

#### 内容域（服务于AI 驱动的后端系统）
- **Content Source（内容来源）**：独立实体，存储爬虫抓取的来源平台信息，以 JSON 格式存储。
- **Product（商品）**：独立实体，存储爬虫抓取的商品信息，以 JSON 格式存储，供后续处理。
- **Deal (临时 JSON)（优惠信息）**：临时存储爬虫抓取的 Deal 数据，结构参考现有设计，不长期保留，最终转化为向量数据。
- **Comment (临时 JSON)（评论）**：临时存储爬虫抓取的评论数据，供向量数据库消费。
- **Vector Data**：向量数据，用于 RAG 检索。

#### 更新后的数据实体列表：
- 商品域（WordPress）：Product、Deal、Review Article、Category、Tag
- 用户域（WordPress）：User、Comment、Follow Relationship
- 内容域（AI 驱动的后端系统）：Content Source、Product、Deal (临时 JSON)、Comment (临时 JSON)

### 1.2 数据域划分

#### 商品域（Product Domain，基于 WordPress）
- **核心职能**：管理商品信息、优惠信息和评测文章，构建内容发布体系。
- **包含实体**：Product、Deal、Review Article、Category、Tag
- **数据来源**：通过 WordPress REST API 从内容域接收数据。

#### 用户域（User Domain，基于 WordPress）
- **核心职能**：管理用户账户、用户行为和社交关系。
- **包含实体**：User、Comment、Follow Relationship
- **数据来源**：WordPress 原生功能 + 插件扩展。

#### 内容域（Content Domain，服务于 AI 驱动的后端系统）
- **核心职能**：采集和处理网络爬虫数据，生成可供 WordPress 消费的内容。
- **包含实体**：Content Source、Product、Deal (临时 JSON)、Comment (临时 JSON)
- **数据输出**：通过向量数据库（Supabase pgvector）和 API 推送到商品域。

### 1.3 数据字典构建

以下是更新后的数据实体数据字典，复用 WordPress 表的部分仅描述扩展字段，未复用的独立实体保持完整定义。

#### 商品域（WordPress）

##### Product（复用 wp_posts）

| 属性 (Attributes) | 数据类型 (Data Type) | 约束 (Constraints) | 描述 (Description) |
|------------------|---------------------|-------------------|-------------------|
| ID | BIGINT | PK, Auto-increment | 主键，WordPress 原生字段 |
| post_title | VARCHAR(255) | NOT NULL | 商品名称 |
| post_content | LONGTEXT | | 商品描述 |
| post_type | VARCHAR(20) | NOT NULL | 固定为 'product' |
| meta_key: brand | VARCHAR(100) | | 品牌（存储在 wp_postmeta） |
| meta_key: asin | VARCHAR(50) | | Amazon 识别号 |
| meta_key: price_normal_180days | DECIMAL | | 180 天常规价格 |
| meta_key: price_lowest | DECIMAL | | 历史最低价格 |
| meta_key: price_average | DECIMAL | | 平均价格 |
| meta_key: image_url | VARCHAR(255) | | 商品主图 URL |
| post_date | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| post_modified | DATETIME | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

##### Deal（复用 wp_posts）

| 属性 (Attributes) | 数据类型 (Data Type) | 约束 (Constraints) | 描述 (Description) |
|------------------|---------------------|-------------------|-------------------|
| ID | BIGINT | PK, Auto-increment | 主键，WordPress 原生字段 |
| post_title | VARCHAR(255) | NOT NULL | Deal 标题 |
| post_content | LONGTEXT | | Deal 详细描述 |
| post_type | VARCHAR(20) | NOT NULL | 固定为 'deal' |
| meta_key: product_id | BIGINT | FK (wp_posts.ID) | 关联商品 ID |
| meta_key: deal_price | DECIMAL | NOT NULL | Deal 价格 |
| meta_key: discount_rate | DECIMAL | | 折扣力度 |
| meta_key: deal_url | VARCHAR(255) | NOT NULL | Deal 链接 |
| meta_key: expiry_time | DATETIME | | 过期时间 |
| meta_key: content_source_id | INT | | 来源平台 ID |
| meta_key: like_count | INT | DEFAULT 0 | 喜欢计数 |
| meta_key: unlike_count | INT | DEFAULT 0 | 不喜欢计数 |
| post_date | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| post_modified | DATETIME | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

##### Review Article（复用 wp_posts）

| 属性 (Attributes) | 数据类型 (Data Type) | 约束 (Constraints) | 描述 (Description) |
|------------------|---------------------|-------------------|-------------------|
| ID | BIGINT | PK, Auto-increment | 主键，WordPress 原生字段 |
| post_title | VARCHAR(255) | NOT NULL | 评测文章标题 |
| post_content | LONGTEXT | | 评测文章内容 |
| post_type | VARCHAR(20) | NOT NULL | 固定为 'review_article' |
| meta_key: product_id | BIGINT | FK (wp_posts.ID) | 关联商品 ID |
| meta_key: like_count | INT | DEFAULT 0 | 喜欢计数 |
| meta_key: unlike_count | INT | DEFAULT 0 | 不喜欢计数 |
| post_date | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| post_modified | DATETIME | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

##### Category（复用 wp_terms 和 wp_term_taxonomy）

| 属性 (Attributes) | 数据类型 (Data Type) | 约束 (Constraints) | 描述 (Description) |
|------------------|---------------------|-------------------|-------------------|
| term_id | BIGINT | PK, Auto-increment | 主键，WordPress 原生字段 |
| name | VARCHAR(200) | NOT NULL | 分类名称 |
| taxonomy | VARCHAR(32) | NOT NULL | 固定为 'category' |
| parent | BIGINT | FK (wp_terms.term_id) | 父级分类 ID |
| description | LONGTEXT | | 分类描述 |

##### Tag（复用 wp_terms 和 wp_term_taxonomy）

| 属性 (Attributes) | 数据类型 (Data Type) | 约束 (Constraints) | 描述 (Description) |
|------------------|---------------------|-------------------|-------------------|
| term_id | BIGINT | PK, Auto-increment | 主键，WordPress 原生字段 |
| name | VARCHAR(200) | NOT NULL | 标签名称 |
| taxonomy | VARCHAR(32) | NOT NULL | 固定为 'post_tag' 或自定义 |
| description | LONGTEXT | | 标签描述 |

#### 用户域（WordPress）

##### User（复用 wp_users）

| 属性 (Attributes) | 数据类型 (Data Type) | 约束 (Constraints) | 描述 (Description) |
|------------------|---------------------|-------------------|-------------------|
| ID | BIGINT | PK, Auto-increment | 主键，WordPress 原生字段 |
| user_login | VARCHAR(60) | NOT NULL, UNIQUE | 用户名 |
| user_email | VARCHAR(100) | NOT NULL, UNIQUE | 邮箱 |
| user_pass | VARCHAR(255) | NOT NULL | 密码哈希值 |
| meta_key: first_name | VARCHAR(50) | | 用户名字（wp_usermeta） |
| meta_key: last_name | VARCHAR(50) | | 用户姓氏 |
| meta_key: profile_image_url | VARCHAR(255) | | 用户头像 URL |
| user_registered | DATETIME | DEFAULT CURRENT_TIMESTAMP | 注册时间 |


##### Comment（复用 wp_comments）

| 属性 (Attributes) | 数据类型 (Data Type) | 约束 (Constraints) | 描述 (Description) |
|------------------|---------------------|-------------------|-------------------|
| comment_ID | BIGINT | PK, Auto-increment | 主键，WordPress 原生字段 |
| comment_post_ID | BIGINT | FK (wp_posts.ID) | 关联 Deal 或 Review Article |
| comment_author | TINYTEXT | NOT NULL | 评论者名称 |
| comment_content | TEXT | NOT NULL | 评论内容 |
| comment_type | VARCHAR(20) | | 评论类型（空值表示普通评论） |
| user_id | BIGINT | FK (wp_users.ID) | 评论用户 ID |
| comment_date | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

##### Follow Relationship（自定义表，需插件支持）

| 属性 (Attributes) | 数据类型 (Data Type) | 约束 (Constraints) | 描述 (Description) |
|------------------|---------------------|-------------------|-------------------|
| follow_id | INT | PK, Auto-increment | 关注 ID |
| follower_user_id | BIGINT | FK (wp_users.ID) | 关注者用户 ID |
| following_user_id | BIGINT | FK (wp_users.ID) | 被关注者用户 ID |
| follow_time | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 关注时间 |

#### 内容域（AI 驱动的后端系统）

##### Content Source

| 属性 (Attributes) | 数据类型 (Data Type) | 约束 (Constraints) | 描述 (Description) |
|------------------|---------------------|-------------------|-------------------|
| content_source_id | INT | PK, Auto-increment | 内容来源 ID |
| source_name | VARCHAR(255) | NOT NULL, UNIQUE | 来源平台名称（如 Amazon AU） |
| domain_url | VARCHAR(255) | NOT NULL, UNIQUE | 来源平台域名 URL |
| crawl_config | JSON | | 爬虫配置 |
| is_active | BOOLEAN | DEFAULT TRUE | 是否启用 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

##### Product（JSON 存储）

| 属性 (Attributes) | 数据类型 (Data Type) | 描述 (Description) |
|------------------|---------------------|-------------------|
| product_id | INT | 商品 ID |
| product_name | VARCHAR(255) | 商品名称 |
| brand | VARCHAR(100) | 品牌 |
| description | TEXT | 商品描述 |
| asin | VARCHAR(50) | Amazon 识别号 |
| prices | JSON | **多区域价格信息**，JSON 结构，例如：  <br/>`{`<br/>`  "AU": {`<br/>`    "normal_180days": 100.00,`<br/>`    "lowest": 80.00,`<br/>`    "average": 90.00`<br/>`  },`<br/>`  "DE": {`<br/>`    "normal_180days": 120.00,`<br/>`    "lowest": 95.00,`<br/>`    "average": 105.00`<br/>`  },`<br/>`  ...`<br/>`}` |
| image_url | VARCHAR(255) | 商品主图 URL |
| created_at | TIMESTAMP | 创建时间 |

##### Deal (临时 JSON)

| 属性 (Attributes) | 数据类型 (Data Type) | 描述 (Description) |
|------------------|---------------------|-------------------|
| deal_id | INT | Deal ID |
| product_id | INT | 关联商品 ID |
| deal_title | VARCHAR(255) | Deal 标题 |
| deal_price | DECIMAL | Deal 价格 |
| discount_rate | DECIMAL | 折扣力度 |
| deal_url | VARCHAR(255) | Deal 链接 |
| expiry_time | TIMESTAMP | 过期时间 |
| description_short | VARCHAR(500) | Deal 简短描述 |
| description_detail | TEXT | Deal 详细描述 |
| image_url | VARCHAR(255) | Deal 图片 URL |
| content_source_id | INT | 来源电商平台 ID |
| created_at | TIMESTAMP | 创建时间 |

##### Comment (临时 JSON)

| 属性 (Attributes) | 数据类型 (Data Type) | 描述 (Description) |
|------------------|---------------------|-------------------|
| comment_id | INT | 评论 ID |
| deal_id | INT | 关联 Deal ID |
| comment_text | TEXT | 评论内容 |
| user_name | VARCHAR(50) | 评论者名称 |
| created_at | TIMESTAMP | 创建时间 |

##### Vector Data

| 属性 (Attributes) | 数据类型 (Data Type) | 约束 (Constraints) | 描述 (Description) |
|------------------|---------------------|-------------------|-------------------|
| vector_id | INT | PK, Auto-increment | 向量数据的唯一标识符 |
| embedding | FLOAT[] / VECTOR | NOT NULL | 向量嵌入数据，用于检索 |
| source_type | ENUM('deal', 'comment') | NOT NULL | 数据来源类型（交易或评论） |
| source_id | INT | NOT NULL | 来源实体的 ID |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

## 2. 构建概念和逻辑数据模型

### 2.1 概念数据模型（Conceptual Data Model）

以下使用 Mermaid 语法描述跨域间的概念数据模型，明确 WordPress和AI 驱动的后端系统之间的关系。

```mermaid
erDiagram
    %% 商品域 (WordPress)
    Product {
        bigint ID PK
        varchar post_title
        longtext post_content
        varchar post_type "product"
    }
    Deal {
        bigint ID PK
        varchar post_title
        longtext post_content
        varchar post_type "deal"
        bigint product_id FK
    }
    ReviewArticle {
        bigint ID PK
        varchar post_title
        longtext post_content
        varchar post_type "review_article"
        bigint product_id FK
    }
    Category {
        bigint term_id PK
        varchar name
        varchar taxonomy "category"
    }
    Tag {
        bigint term_id PK
        varchar name
        varchar taxonomy "post_tag"
    }

    %% 用户域 (WordPress)
    User {
        bigint ID PK
        varchar user_login
        varchar user_email
        varchar user_pass
    }
    Comment {
        bigint comment_ID PK
        bigint comment_post_ID FK
        text comment_content
        bigint user_id FK
    }
    FollowRelationship {
        int follow_id PK
        bigint follower_user_id FK
        bigint following_user_id FK
    }

    %% 内容域 (AI 驱动的后端系统)
    ContentSource {
        int content_source_id PK
        varchar source_name
        varchar domain_url
        json crawl_config
    }
    Product_JSON {
        int product_id
        varchar product_name
        varchar brand
        text description
    }
    Deal_JSON {
        int deal_id
        int product_id
        varchar deal_title
        decimal deal_price
        varchar deal_url
    }
    Comment_JSON {
        int comment_id
        int deal_id
        text comment_text
    }

    Vector_Data {
        int vector_id PK
        float[] embedding
        enum source_type
        int source_id
        timestamp created_at
    }
    
    %% 跨域关系
    Product ||--o{ Deal : "关联"
    Product ||--o{ ReviewArticle : "评测"
    Product ||--o{ Category : "属于"
    Product ||--o{ Tag : "拥有"
    Deal ||--o{ Tag : "拥有"
    ReviewArticle ||--o{ Tag : "拥有"
    Comment ||--o{ Deal : "评论对象"
    Comment ||--o{ ReviewArticle : "评论对象"
    User ||--o{ Comment : "评论者"
    User ||--o{ FollowRelationship : "关注"
    User ||--o{ FollowRelationship : "被关注"

    %% 内容域 -> 商品域 (通过 API)
    Product_JSON ||--o{ Deal_JSON : "关联"
    Deal_JSON ||--o{ Comment_JSON : "拥有"
    Deal_JSON ||--o{ ContentSource : "来源于"
    Product_JSON ||..o{ Product : "生成并推送(API)"
    Deal_JSON ||..o{ Deal : "生成并推送(API)"
    ReviewArticle ||..o{ Product_JSON : "参考生成(API)"

    %% 向量数据关系
    Deal_JSON ||--o{ Vector_Data : "生成"
    Comment_JSON ||--o{ Vector_Data : "生成"
```

说明：
- 商品域和用户域实体基于 WordPress 表结构，字段简化展示。
- 内容域实体以 JSON 形式存储，临时处理后通过 WordPress REST API（如 /wp-json/wp/v2/posts）推送到商品域。
- 向量数据库（Supabase pgvector）未在图中显示，但用于存储 Deal_JSON 和 Comment_JSON 的向量数据，供 RAG 处理。

### 2.2 逻辑数据模型（Logical Data Model）

逻辑数据模型基于概念模型，进一步细化字段和约束。由于篇幅限制，仅列出关键实体的逻辑模型，其余可参考数据字典。

#### 商品域（WordPress）

##### Product（复用 wp_posts）

| 属性 (Attribute) | 数据类型 (Data Type) | 约束 (Constraints) | 描述 (Description) |
|-----------------|---------------------|-------------------|-------------------|
| ID | BIGINT | PK, Auto-increment | 主键 |
| post_title | VARCHAR(255) | NOT NULL | 商品名称 |
| post_content | LONGTEXT | | 商品描述 |
| post_type | VARCHAR(20) | NOT NULL | 固定为 'product' |
| post_date | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| post_modified | DATETIME | DEFAULT CURRENT_TIMESTAMP | 更新时间 |
| meta_key: brand | VARCHAR(100) | | 品牌（wp_postmeta） |

#### 用户域（WordPress）

##### User（复用 wp_users）

| 属性 (Attribute) | 数据类型 (Data Type) | 约束 (Constraints) | 描述 (Description) |
|-----------------|---------------------|-------------------|-------------------|
| ID | BIGINT | PK, Auto-increment | 主键 |
| user_login | VARCHAR(60) | NOT NULL, UNIQUE | 用户名 |
| user_email | VARCHAR(100) | NOT NULL, UNIQUE | 邮箱 |
| user_pass | VARCHAR(255) | NOT NULL | 密码哈希值 |

#### 内容域（AI 驱动的后端系统）

##### Content Source

| 属性 (Attribute) | 数据类型 (Data Type) | 约束 (Constraints) | 描述 (Description) |
|-----------------|---------------------|-------------------|-------------------|
| content_source_id | INT | PK, Auto-increment | 内容来源 ID |
| source_name | VARCHAR(255) | NOT NULL, UNIQUE | 来源平台名称 |
| domain_url | VARCHAR(255) | NOT NULL, UNIQUE | 来源平台域名 URL |
| crawl_config | JSON | | 爬虫配置 |

##### Vector Data

**生成方式**：
- 每次爬取新的 Deal 或 Comment 数据后，系统通过嵌入模型（如 BERT 或其他预训练模型）自动生成对应的向量嵌入。

**更新频率**：
- 与数据爬取频率保持同步，通常为每日或根据业务需求调整。

**使用场景**：
- 在 RAG 模型中，通过向量数据库（如 Supabase 的 pgvector）进行高效检索，以提供相关交易或评论内容。

## 3. 数据完整性和性能优化

### 3.1 数据约束
- WordPress 域：利用 NOT NULL、UNIQUE 等原生约束，wp_postmeta 和 wp_usermeta 的扩展字段需通过代码校验。
- 内容域：JSON 数据在存储前通过程序校验（如确保 deal_price >= 0）。

### 3.2 存储过程和触发器
- WordPress 域：通过插件或主题自定义触发器（如更新 post_modified）。
- 内容域：触发器用于同步 JSON 数据到向量数据库。

### 3.3 查询优化
- WordPress 域：为 wp_posts.post_type、wp_comments.comment_post_ID 创建索引。
- 内容域：向量数据库使用 pgvector 优化 RAG 查询。
