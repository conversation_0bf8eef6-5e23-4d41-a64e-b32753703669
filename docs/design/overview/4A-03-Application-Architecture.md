### 1. 架构设计原则

- **模块化:** 系统组件模块化设计，职责明确。
- **自动化:** 利用 AI 技术提高效率。
- **高性能:** 保证平台高性能和快速响应。
- **可扩展性:** 架构具备可扩展性。

### 2. 核心关注点细化与明确

#### 2.1 功能模块划分 (模块关系最终最终调整版 - Human-agents & Automated-utilities Block 划分)

本次更新将 "内容运营部门驱动" 的 AI 后端系统细分为 **Human-agents block** 和 **Automated-utilities block** 两个核心逻辑块，更清晰地展现了人工 Agent 和程序化工具之间的分工与协作：

- **网站系统 (WordPress): 面向最终用户 (End-User) 的完整平台**
  - **WordPress 前端:** 用户交互入口。
  - **WordPress 后端 PHP:** WordPress PHP 后端系统。
  - **数据库系统 (Supabase - WordPress表):** WordPress 网站数据库。

- **AI 驱动的后端系统: 内容运营部门驱动**
  - **内容运营部门:** 平台内容生产和运营核心部门，**包含以下两个并行的逻辑块:**
    - **Human-agents block:** **由以下人工 Agent 组成，负责业务流程的orchestration和内容运营决策:**
      - **运营经理 Agent (Autogen Orchestrator):** **核心 Orchestrator Agent，负责：**
        - 发起主要的业务流程。
        - 制定运营计划 (plan)。
        - 驱动 爬虫Util 收集数据。
        - 驱动 数据处理专员Agent 加工处理原始数据。
        - 驱动 内容生成专员Agent 生成内容。
        - 要求 内容评审专员Agent 反馈内容评审意见。
        - 驱动 内容生成专员Agent 完善内容。
        - 调用 WordPressUtil 提交最终内容 (AI 生成内容)。
        - 驱动 内容评审专员Agent 评审 end-user 发布内容 (Deal, Article, Comments 等)。
        - 调用 WordPressUtil 提交最终内容 (用户审核通过内容)。
      - **数据处理专员 Agent:** **负责：**
        - 调用 DBConnector -> Supabase (AI运营相关表) 进行数据交互。
        - 调用 数据处理Util 进行原始数据加工处理。
        - 生成加工后的数据，供内容生成使用。
      - **内容生成专员 Agent:** **负责：**
        - 调用 DBConnector -> Supabase (AI运营相关表) 生成 Deal 和 Articles 等平台内容。
        - 调用 WordPressUtil 获取 end-user 发布内容，进行必要的互动 (例如回复用户评论)。
        - 根据 内容评审专员Agent 的反馈完善生成内容。
        - 调用 WordPressUtil 提交最终内容 (AI 生成内容)。
      - **内容评审专员 Agent:** **负责：**
        - **直接进行用户内容评审** (Deal, Article, Comments 等)。
        - 调用 WordPressUtil 获取 end-user 发布内容 (Deal, Article, Comments 等) 进行内容评审。
        - 向 运营经理 Agent 和 内容生成专员Agent 反馈评审意见。

    - **Automated-utilities block:** **由以下程序化工具 (Utils) 组成，为 Human-agents block 提供底层工具支撑:**
      - **爬虫 Util (Scrapy):** 负责从电商平台抓取 Deal 相关数据。
      - **数据处理 Util:** 负责对原始数据进行清洗、转换、结构化等预处理操作。
      - **WordPress Util:** **负责：**
        - 与 WordPress 网站系统进行 API 交互 (例如**内容发布**，获取用户内容等)。 **本质上是对 WordPress REST API 的工具封装。**
        - 作为 Human-agents block 与 WordPress 网站系统交互的桥梁。
      - **DBConnector:** **负责：**
        - 连接和操作数据库系统 (Supabase - AI运营相关表)。
        - 作为 Human-agents block 与 Supabase 数据库 AI运营相关表 交互的桥梁。

  - **数据库系统 (Supabase - AI运营相关表):** AI 驱动的后端系统运营数据库。

- **数据库系统 (Supabase Cloud Service): 数据存储中心 (拆分)**
  - **Supabase 数据库 WordPress表:** 服务于 WordPress 网站系统。
  - **Supabase 数据库 AI运营相关表:** 服务于 AI 驱动的后端系统。

**功能模块图 (Mermaid - 模块关系最终最终调整版 - Human-agents & Automated-utilities Block 划分):**

```mermaid
graph LR
    subgraph 网站系统WordPress
        A[WordPress前端]
        AA[WordPress后端PHP]
        AL[Supabase数据库WordPress表]
        AA --> AL
        A --> AA
    end
    subgraph AI驱动的后端系统
        subgraph 内容运营部门
            subgraph HumanAgentsBlock[Human-agents block]
                B[运营经理Agent]
                BA[数据处理专员Agent]
                BB[内容生成专员Agent]
                BC[内容评审专员Agent]
            end
            subgraph AutomatedUtilsBlock[Automated-utilities block]
                C[爬虫Util]
                D[数据处理Util]
                E[WordPressUtil]
                F[DBConnector]
            end
            BL[Supabase数据库AI运营相关表]
        end
        内容运营部门 --> HumanAgentsBlock
        内容运营部门 --> AutomatedUtilsBlock
        内容运营部门 --> BL
        F --> BL
        BB --> BL
        BA --> BL
        BB --> E
        BC --> E
        B --> C
        B --> BA
        B --> BB
        B --> BC
        BA --> D
        BB --> AA
        E --> AA
    end
    A --> A[用户访问/互动]
    B --> B[业务流程发起]
    BA --> BA[数据加工处理]
    BB --> BB[内容生成]
    BC --> BC[内容评审]
    C --> C[数据采集]
    D --> D[数据处理]
    E --> E[API交互]
    F --> F[数据库连接]
```
### 2.2 组件交互 (模块关系最终最终调整版 - Human-agents & Automated-utilities Block 划分)

组件交互方式在最新版本中，更细致地展现了 Human-agents block 和 Automated-utilities block 之间的协作关系：

#### WordPress 网站系统内部交互
- 前端与后端交互: WordPress 前端 JavaScript 与 WordPress 后端 PHP 交互
- 后端与数据库交互: WordPress 后端 PHP 与 Supabase 数据库 WordPress表 交互

#### AI 驱动的后端系统内部交互
- **内容运营部门 协调 Human-agents block 和 Automated-utilities block:** 内容运营部门作为顶层模块，协调和管理并行的 "Human-agents block" 和 "Automated-utilities block" 协同完成内容生产和运营任务。
- **Human-agents block 驱动 Automated-utilities block:** "Human-agents block" 中的各个 Agent 驱动 "Automated-utilities block" 中的各个 Utils 执行具体的程序化任务。
- **Agent 与 Util 交互:** "Human-agents block" 中的 Agent 通过函数调用的方式调用 "Automated-utilities block" 中 Util 提供的功能接口。
- **Util 之间的数据传递:** "Automated-utilities block" 内部，各个 Util 之间的数据传递可以通过直接函数返回值或消息队列等方式实现。
- **Util 与数据库/WordPress 交互:** DBConnector Util 负责与数据库交互，WordPress Util 负责与 WordPress 网站系统的 API 交互。

#### 组件交互流程示例 - 运营经理 Agent 驱动 Deal 信息自动化抓取和发布 (区域化)

```mermaid
sequenceDiagram
    participant OPS as 内容运营部门
    participant MGR as 运营经理Agent
    participant SCR as 爬虫Util(Region Config)
    participant DPA as 数据处理专员Agent
    participant DPU as 数据处理Util
    participant DBC as DBConnector
    participant DB as Supabase数据库
    participant CGA as 内容生成专员Agent
    participant WPU as WordPressUtil(Region API)
    participant WPS as WordPress后端PHP

    OPS->>MGR: 触发 **澳洲区域** Deal信息抓取和发布流程
    MGR->>SCR: 调用爬虫Util启动 **澳洲区域** Deal爬取任务 (**AU Region Config**)
    SCR-->>DPA: 返回原始Deal数据
    DPA->>DPU: 调用数据处理Util进行数据预处理
    DPU-->>DPA: 返回预处理后的数据
    DPA->>DBC: 调用DBConnector写入Deal数据
    DBC-->>DB: 写入Deal数据
    DPA-->>CGA: 传递特征数据
    CGA->>DB: 调用DBConnector检索商品/Deal相关信息
    DB-->>CGA: 返回检索结果
    CGA->>CGA: AI生成Deal描述
    CGA->>WPU: 调用WordPressUtil传递生成的内容 (**AU Region API**)
    WPU->>WPS: 调用WordPress REST API发布Deal信息 (**AU Region API**)
    WPS-->>DB: 写入/更新WordPress数据库
```

### 2.3 业务流程实现 (模块关系最终最终调整版 - Human-agents & Automated-utilities Block 划分)

业务流程实现部分在最终最终调整版架构中，更细致地展现了 Human-agents block 和 Automated-utilities block 的协作关系，以及各个 Agent 和 Util 的具体职责：
#### 2.3.1 运营经理 Agent 驱动 Deal 信息自动化抓取和发布流程

```mermaid
sequenceDiagram
    participant OPS as 内容运营部门
    participant MGR as 运营经理Agent
    participant SCR as 爬虫Util
    participant DPA as 数据处理专员Agent
    participant DPU as 数据处理Util
    participant DBC as DBConnector
    participant DB as Supabase数据库
    participant CGA as 内容生成专员Agent
    participant WPU as WordPressUtil
    participant WPS as WordPress后端PHP

    OPS->>MGR: 触发Deal信息抓取和发布流程
    MGR->>SCR: 调用爬虫Util启动Deal爬取任务
    SCR-->>DPA: 返回原始Deal数据
    DPA->>DPU: 调用数据处理Util进行数据预处理
    DPU-->>DPA: 返回预处理后的数据
    DPA->>DBC: 调用DBConnector写入Deal数据
    DBC-->>DB: 写入Deal数据
    DPA-->>CGA: 传递特征数据
    CGA->>DB: 调用DBConnector检索商品/Deal相关信息
    DB-->>CGA: 返回检索结果
    CGA->>CGA: AI生成Deal描述
    CGA->>WPU: 调用WordPressUtil传递生成的内容
    WPU->>WPS: 调用WordPress REST API发布Deal信息
    WPS-->>DB: 写入/更新WordPress数据库
```

#### 2.3.2 用户发布评论流程

```mermaid
sequenceDiagram
    participant USER as 用户
    participant WPF as WordPress前端
    participant WPS as WordPress后端PHP
    participant DB as 数据库系统WordPress表

    USER->>WPF: 提交评论
    WPF->>WPS: 发送发布评论请求
    WPS->>WPS: 评论内容校验和安全过滤
    WPS->>DB: 写入评论数据
    DB-->>WPS: 返回评论写入结果
    WPS-->>WPF: 返回发布评论API响应
    WPF->>USER: 页面提示评论发布结果
    WPF->>DB: 更新评论计数(异步)
```

#### 2.3.3 用户浏览 Deal 详情页流程

```mermaid
sequenceDiagram
    participant USER as 用户
    participant WPF as WordPress前端
    participant WPS as WordPress后端PHP
    participant DB as 数据库系统WordPress表

    USER->>WPF: 请求Deal详情页
    WPF->>WPS: 请求Deal详情数据
    WPS->>DB: 查询Deal基本信息
    DB-->>WPS: 返回Deal基本信息
    WPS->>DB: 查询商品信息
    DB-->>WPS: 返回商品信息
    WPS->>DB: 查询Deal评论
    DB-->>WPS: 返回Deal评论列表
    WPS-->>WPF: 返回Deal详情API响应
    WPF->>USER: 展示Deal详情页
```

#### 2.3.4 用户 Deal 爆料内容审核与发布流程

```mermaid
sequenceDiagram
    participant USER as 用户
    participant WPF as WordPress前端
    participant WPS as WordPress后端PHP
    participant DB as 数据库系统WordPress表
    participant OPS as 内容运营部门
    participant HAG as 内容运营部门HumanAIAgents
    participant REV as 内容评审专员Agent
    participant WPU as WordPressUtil
    participant MGR as 运营经理Agent

    USER->>WPF: 提交用户Deal爆料
    WPF->>WPS: 保存用户Deal爆料(待审核)
    WPS->>DB: 写入用户Deal爆料
    DB-->>WPS: 返回写入结果
    WPS-->>WPF: 返回提交成功响应
    WPF->>USER: 显示提交成功提示
    HAG->>WPS: 后台审核用户Deal爆料
    WPS->>HAG: 展示待审核列表
    HAG->>REV: 触发内容评审
    REV->>WPU: 获取用户Deal爆料内容
    WPU-->>REV: 返回爆料内容
    REV->>REV: 进行内容评审
    REV->>WPS: 反馈评审结果
    WPS->>DB: 更新爆料状态
    DB-->>WPS: 返回状态更新结果
    alt 审核通过
        MGR->>WPU: 调用发布爆料
        WPU->>WPS: 发布Deal信息
    else 审核拒绝
        HAG->>WPS: 通知用户审核拒绝
    end
    WPS-->>HAG: 返回审核操作结果
```
#### 2.3.5 内容运营团队回复用户评论互动流程

```mermaid
sequenceDiagram
    participant OPS as 内容运营部门
    participant HAG as 内容运营部门HumanAIAgents
    participant WPF as WordPress前端
    participant USER as 用户

    HAG->>WPF: 浏览用户评论区
    WPF->>HAG: 展示用户评论列表
    HAG->>WPF: 回复用户评论
    WPF->>USER: 用户看到互动回复
```
#### 2.3.6 商品知识库构建与更新
- **核心策略：**  关键词与查询优化 + 分层调用策略 + 结果过滤与去重 + 数据缓存与复用 + 多源数据融合 + 自动化后处理 + 成本监控与调优

  [Click to view the design document](./4A-03-AA-RAG-KnowledgeBase.md)

### 2.4 应用层面的扩展性、可维护性、安全性

#### 扩展性

- **模块化设计:** 方便模块扩展和替换
- **API 驱动架构:** AI 后端与 WordPress 网站系统 API 交互
- **云计算平台:** 云平台弹性伸缩
- **微服务化演进 (未来考虑):** AI 后端系统内部模块微服务化

#### 可维护性

- **分层架构:** WordPress 网站系统 + AI 后端系统 分层架构
- **模块化设计:** 模块高内聚，低耦合
- **代码规范和文档:** 统一代码规范，清晰文档
- **自动化测试:** 完善自动化测试
- **监控和日志:** 完善监控和日志

#### 安全性

- **身份认证和授权:** WordPress 用户系统
- **API 安全:** WordPress REST API 安全性
- **数据安全:** WordPress 数据库和 AI 运营相关数据库 安全性
- **安全漏洞防范:** WordPress 网站系统和 AI 后端系统 安全漏洞防范
- **内容安全:** 内容审核机制 (已扩展，包含用户生成内容审核) (内容运营部门 + Human-agents block + Automated-utilities block 协同)
