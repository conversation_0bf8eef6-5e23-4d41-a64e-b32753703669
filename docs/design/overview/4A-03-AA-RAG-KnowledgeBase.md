# RAG 知识库构建流程：

## 1. 产品信息输入 (Product Input):

- **数据来源**: 从竞品平台爬取的 Deal 和 Product 信息，包括产品名称、型号、品牌、产品类别等核心属性。
- **数据存储:** 将竞品平台爬取的产品信息存储到数据库中，作为后续 Google Search API 查询的基础数据。

## 2. 关键词与查询优化 (Query Optimization):

### 2.1 结构化搜索词生成:
- **策略:** 结合产品属性 (品牌、型号、核心功能) 和评测关键词 (如 "review 2024", "best for [场景]", "vs [竞品]", "pros and cons")，使用动态查询模板，生成精准的结构化搜索词。
- **示例模板:** "{product_name} {product_model} review {year} {comparison} {site_restriction}"
- **关键词库:** 维护评测关键词库 (review, test, 评测, 体验, 优缺点, pros and cons 等) 和权威评测网站白名单 (CNET, TechRadar, The Verge 等)。

### 2.2 高级搜索语法应用:
- **策略:** 在查询中应用高级搜索运算符，例如 site:, inurl:, intitle:, 布尔逻辑 AND/OR，引号 "" 等，进一步提高查询精准度，过滤低质量结果。
- **示例:** "Bose QuietComfort 45" AND ("noise cancellation test" OR "battery life") site:techradar.com

## 3. 分层调用策略 (Tiered Calling Strategy):

### 3.1 优先级分级 (Priority Tiering):
- **策略:** 根据产品热度 (销量、竞品平台曝光度) 对产品进行优先级分级 (高、中、低)。
- **API 配额分配:** 为不同优先级的产品分配不同的 Google Search API 调用配额，头部高热度产品分配更多配额，确保重要产品评测信息的全面覆盖。

### 3.2 分阶段查询 (Multi-Stage Querying):
- **初筛阶段:** 使用宽泛关键词 (如产品名 + "review") 进行初步搜索，快速获取主流评测文章，用于构建基础知识库。
- **深度阶段:** 分析初筛结果，提取高频评测维度 (例如某款手机的 "拍照性能", "电池续航")，针对性地补充细节查询，例如 "iPhone 15 Pro Max" camera review, "iPhone 15 Pro Max" battery test，深入挖掘评测细节。

### 3.3 增量更新机制 (Incremental Update):
- **策略:** 对于已存在于知识库中的产品，定期进行增量更新，仅搜索过去 1-3 个月内的新内容 (通过 before:/after: 时间过滤)，减少重复调用，保持知识库的时效性。
- **更新频率**: 头部产品更新频率较高 (例如每月更新)，非头部产品更新频率较低 (例如每季度更新)。

## 4.  Google Search API 调用 (Google Search API Call):

- **API 选择**: 使用 Google Custom Search API 或 Programmable Search Engine API (根据配额和功能需求选择)。
- **API 参数配置:**设置合适的 API 参数，例如 q (查询关键词), num (返回结果数量，建议适中，例如 10-20 条), siteSearch (限定搜索站点), dateRestrict (时间范围), lr (语言限定), cr (地区限定) 等。
- **API 调用频率控制:** 严格控制 API 调用频率，设置合理的请求间隔，避免超出 API 配额或被 Google 封禁。

## 5.  结果过滤与去重 (Result Filtering & De-duplication):

### 5.1 权威域名白名单过滤 (Authority Whitelist Filtering):
- **策略:** 维护权威评测媒体和垂直领域 KOL 博客的域名白名单。
- **白名单优先:** 优先收录来自白名单域名的搜索结果，可以使用 site: 运算符直接限定搜索白名单站点。

### 5.2 内容相似度去重 (Content Similarity De-duplication):
- **策略:** 对 API 返回的 URL 和摘要进行文本哈希 (例如 SimHash 或 MinHash) 计算，或使用语义相似度算法 (例如 Sentence-BERT 相似度)。
- **去重阈值:** 设定相似度阈值 (例如 90%)，将相似度超过阈值的结果判断为重复内容，进行去重，避免重复收录。

### 5.3 元数据过滤 (Metadata Filtering):
- **策略:** 根据页面标题、摘要等元数据中的关键词 (例如 "hands-on", "benchmark", "广告", "论坛")，使用关键词黑名单或规则匹配，排除非评测类结果 (例如广告页面、论坛灌水帖子、商品列表页)。

## 6.  数据缓存与复用 (Data Caching & Reuse):

### 6.1 本地缓存数据库 (Local Cache Database):
- **策略:** 构建本地缓存数据库 (例如 PostgreSQL 或 Redis)，存储已爬取的评测文章的 URL、摘要、关键信息 (评分、结论)、全文内容以及查询时间戳。
- **缓存键:** 使用 Google Search API 查询关键词作为缓存键。

### 6.2 缓存更新策略 (Cache Update Strategy):
- **缓存命中检查**: 在发起 Google Search API 查询前，先检查本地缓存数据库中是否已存在相同查询关键词的缓存结果。
- **缓存复用**: 如果缓存命中且缓存未过期，直接复用缓存结果，避免重复 API 调用。
- **缓存过期策略**: 对非头部产品，设定较长的缓存刷新周期 (例如每月 1 次)。 对头部产品，可以按需更新或设定较短的刷新周期 (例如每周更新)。 可以根据 last_crawled_at 字段判断缓存是否过期。

## 7.  多源数据融合 (Multi-source Data Fusion):

### 7.1 竞品平台评论融合:
- **策略**: 将竞品平台用户评论中提取的高频关键词 (例如 "电池续航差", "屏幕显示效果好")，作为补充搜索词，与产品信息组合，定向查找评测文章中关于这些用户关注点的专业分析和评测。

### 7.2 社交媒体/论坛整合 (Social Media/Forum Integration - 可选):
- **策略**: 如果 API 配额允许，可以考虑通过 Reddit, Twitter 等社交媒体或论坛 API，获取用户对产品的真实反馈和讨论，与专业评测进行交叉验证，补充更全面的用户视角。
- **API 配额分配**: 社交媒体/论坛 API 调用需要额外配额，需要根据实际情况评估是否引入，并合理分配 API 配额。

## 8.  自动化后处理 (Automated Post-processing):

### 8.1 关键信息提取 (NLP - Key Information Extraction):
- **策略**: 使用自然语言处理 (NLP) 模型 (例如 spaCy, Stanford CoreNLP, 或云端 NLP API) 对爬取的评测文章全文进行处理。
- **NER 模型应用**: 使用命名实体识别 (NER) 模型，提取评测文章中的产品参数 (例如 "120Hz 屏幕", "5000mAh 电池")、优缺点 (例如 "优点：拍照清晰", "缺点：发热严重")、评分 (例如 "4.5 星", "9/10 分") 等关键信息。
- **结构化存储**: 将提取的关键信息结构化存储到 document_metadata 表或其他相关数据表中，方便后续 RAG 系统检索和利用。

### 8.2 时效性加权 (Recency Weighting):
- **策略**: 为近期 (例如 3 个月内) 发布的评测文章赋予更高的权重，在 RAG 系统检索时，优先返回时效性更强、更贴合当前市场情况的最新评测结论。
- **权重计算**: 可以根据文章发布时间，使用线性或指数衰减函数计算时效性权重，并在 RAG 检索排序时考虑该权重。

## 9.  知识库更新与存储 (Knowledge Base Update & Storage):

- **数据入库**: 将经过筛选、去重、精炼后的评测文章内容 (content) 和向量 Embedding (embedding) 存储到 knowledge_documents 表中。
- **元数据入库**: 将提取的结构化信息 (产品参数、优缺点、评分等) 以及文章元数据 (来源 URL, 发布时间等) 存储到 document_metadata 表中。
- **向量索引更新**: 在数据入库后，及时更新向量数据库的索引，确保新加入的文档能够被 RAG 系统检索到。

## 10. 成本监控与调优 (Cost Monitoring & Optimization):

### 10.1 API 配额动态分配 (Dynamic Quota Allocation):
- **监控指标**: 实时监控 Google Search API 的调用量、剩余配额、调用成功率、返回结果质量等指标。
- **动态调整**: 如果发现部分查询关键词或产品类别的查询返回低质量结果较多，或 API 调用失败率升高，可以动态减少该类查询的 API 配额，将配额转移到更重要的产品或查询上，提高 API 利用率。

```mermaid
graph LR
    A["产品信息输入 (竞品平台数据)"] --> B{关键词与查询优化};
    B --> C{分层调用策略};
    C --> D[Google Search API 调用];
    D --> E{数据缓存检查};
    E -- 缓存命中 --> F[从缓存获取结果];
    E -- 缓存未命中 --> G{结果过滤与去重};
    G --> H{"数据存储 (Knowledge Documents, Metadata)"};
    F --> H;
    H --> I{多源数据融合};
    I --> J{"自动化后处理 (NLP 关键信息提取, 时效性加权)"};
    J --> K["知识库更新 (向量数据库)"];
    K --> L["RAG 应用 (知识检索与生成)"];
    D --> M{成本监控与调优};
    M --> C;

    style B fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#f9f,stroke:#333,stroke-width:2px
    style G fill:#f9f,stroke:#333,stroke-width:2px
    style I fill:#f9f,stroke:#333,stroke-width:2px
    style J fill:#f9f,stroke:#333,stroke-width:2px
    style M fill:#f9f,stroke:#333,stroke-width:2px

    subgraph 核心流程
    B --> C --> D --> E --> G --> H --> I --> J --> K --> L
    end

    subgraph 辅助流程
    D --> M
    end

    subgraph 多源数据融合
    H --> I_1[竞品平台评论融合];
    H --> I_2["社交媒体/论坛整合 (可选)"];
    I_1 --> I;
    I_2 --> I;
    style I_1 fill:#ccf,stroke:#333,stroke-width:1px,dasharray:5 5
    style I_2 fill:#ccf,stroke:#333,stroke-width:1px,dasharray:5 5
    end
```