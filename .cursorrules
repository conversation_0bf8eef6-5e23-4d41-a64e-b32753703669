# AutoGen 代码规范规则

## 1. 导入规则
- 必须使用标准的 AutoGen 0.4 导入方式
- 禁止使用旧版本 (v0.2) 的导入方式
- 基础导入必须包含：
python
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient

## 2. Agent 定义规则
- 所有 Agent 必须继承自 autogen_agentchat.agents 中的基类
- Agent 初始化必须包含:
  - name: str
  - system_message: str
  - model_client: OpenAIChatCompletionClient
- 工具集成必须使用新版本方式，通过 tools 参数传入
- 默认Autogen的客户端必须使用Gemini构建，代码参考如下：
    model_client = OpenAIChatCompletionClient(
        model="gemini-2.0-flash-exp", 
        api_key=os.getenv('GEMINI_API_KEY'),
        model_info={
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": "unknown",
        })

## 3. 消息处理规则
- 必须使用 autogen_agentchat.messages 中定义的消息类型
- 异步操作必须使用 async/await 语法
- 必须正确处理 CancellationToken

## 4. 工具集成规则
- 使用新版本的工具集成方式，避免使用 v0.2 的 register_function
- 工具函数必须有清晰的类型注解
- 必须通过 tools 参数传入 Agent
- 工具函数必须使用 async 定义

## 5. 爬虫以及代理配置规则
- 必须使用scrape.do的代理服务，代理URL配置必须参考如下：
    base_url = "https://au.camelcamelcamel.com/popular?deal=1&p=1"
    target_url = base_url
    if use_proxy:
        target_url = f"http://api.scrape.do?token={os.getenv('SCRAPE_DO_API_KEY')}&url={urllib.parse.quote(base_url)}"

- 必须使用 BrowserConfig 和 CrawlerRunConfig 进行配置
- 配置参数必须完整且类型正确
- 必须处理代理认证信息
- 必须使用 Crawl4AI 的最新版本
- BrowserConfig和CrawlerRunConfig必须参考如下使用：
    browser_cfg = BrowserConfig(
        verbose=True,
        headers={
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
        },
    )
    crawl_config = CrawlerRunConfig(
        word_count_threshold=5,
        exclude_external_links=True,
        remove_overlay_elements=True,
        cache_mode=CacheMode.DISABLED,
        page_timeout=60000,
        wait_until='commit'
    )
- 代理URL配置必须不能自动添加render=True

## 6. 错误处理规则
- 必须实现适当的错误处理机制
- 必须使用 AutoGen 定义的异常类型
- 必须记录关键错误信息

## 7. 状态管理规则
- 必须使用 autogen_agentchat.state 中的状态类
- 必须正确处理状态保存和恢复
- 必须处理会话状态的持久化

## 8. 文档规则
- 必须包含清晰的文档字符串
- 必须说明参数类型和返回值
- 必须包含使用示例

## 9. 测试规则
- 必须包含单元测试，自动在项目根目录下生成test目录，并生成对应的测试文件
- 必须测试异常情况
- 必须测试工具集成

## 10. 版本兼容性规则
- 禁止混用不同版本的 API
- 必须使用 v0.4 版本的特性
- 必须移除已废弃的功能使用

## 11. 配置文件规则
- 配置文件必须使用：ai-backend-system/config/config.yaml
- 生成代码时需要优先遍历配置文件中的内容，并使用配置文件中匹配的配置
- 配置文件中的值应该优先于代码中的硬编码值
- 所有区域相关的代码必须使用配置文件中定义的区域列表
- 配置文件中的参数应该通过专门的配置加载模块访问，而不是直接解析
- 配置更改应该不需要修改代码，只需更新配置文件
- 配置文件应该包含合理的默认值，以确保系统在缺少特定配置时仍能正常运行
- 配置文件中的敏感信息（如API密钥）应该使用环境变量引用，而不是直接存储

## 12. 数据库表命名规则
- 所有 ai-backend-system 相关的表必须使用 'backend_' 前缀
- 表命名必须遵循 snake_case 格式
- 示例表名：
  - backend_products
  - backend_products_embeddings
  - backend_user_preferences
  - backend_search_history
- 视图命名也必须遵循相同规则：
  - backend_active_products
  - backend_discounted_products
  - backend_category_products