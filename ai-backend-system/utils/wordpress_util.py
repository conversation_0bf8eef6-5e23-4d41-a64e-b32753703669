# ai-backend-system/utils/wordpress_util.py
"""
定义 WordPress Util (WordPressUtil) 类 - **已更新，支持多区域 WordPress API 配置**.

该 Util 封装了 WordPress REST API 的常用操作，提供统一的接口与不同区域的 WordPress 网站系统进行交互 (例如内容发布，获取用户内容等)。
本质上是对 WordPress REST API 的工具函数封装，并增强了对多区域 WordPress 站点的支持.

核心功能包括 (已更新支持多区域):
- **[多区域支持]** 根据区域代码动态选择 WordPress API Endpoint
- 发布 Deal 信息到 WordPress (创建 WordPress Post，post_type='deal') - 支持多区域
- 发布评测文章到 WordPress (创建 WordPress Post，post_type='review_article') - 支持多区域
- 获取 WordPress 网站上的用户评论 (例如获取指定 Post ID 的评论列表) - 支持多区域
- (可选) 获取 WordPress 分类列表、标签列表、用户信息等 - 支持多区域
- (可选) 更新 WordPress 文章、删除 WordPress 文章等 - 支持多区域
"""
import requests
from requests.auth import HTTPBasicAuth # For Basic Authentication if needed
from typing import Dict, Optional

class WordPressUtil:
    """
    WordPress Util, 负责与 WordPress 网站系统进行 API 交互 (例如内容发布，获取用户内容等) - **已更新，支持多区域配置**.
    本质上是对 WordPress REST API 的工具封装，并增强了多区域支持.
    """
    def __init__(self, wordpress_api_configs: Dict[str, Dict[str, str]]):
        """
        初始化 WordPressUtil 实例 - **已更新，支持多区域 WordPress API 配置**.

        Args:
            wordpress_api_configs (Dict[str, Dict[str, str]]): 多区域 WordPress API 配置字典.
                字典的键为区域代码 (例如 "au", "de", "us")，值为该区域的 WordPress API 配置字典，包含：
                    - "api_url" (str): WordPress REST API 根 URL (例如: http://your-wordpress-site.com/wp-json/wp/v2/). 务必以 `/` 结尾。
                    - "username" (str, optional): WordPress 用户名，用于需要身份验证的 API 请求 (例如发布文章)。默认为 None，表示不使用身份验证 (适用于公开 API，例如获取评论列表)。
                    - "password" (str, optional): WordPress 密码，与 username 配合使用，用于身份验证。默认为 None.

        示例配置结构 (config/config.yaml):
        wordpress_apis:
          au: # 澳洲区域配置
            api_url: "https://au.example.com/wp-json/wp/v2/"
            username: "au_api_user"
            password: "au_api_password"
          de: # 德国区域配置
            api_url: "https://de.example.com/wp-json/wp/v2/"
            username: "de_api_user"
            password: "de_api_password"
          us: # 美国区域配置 (示例，可以根据需要添加更多区域)
            api_url: "https://us.example.com/wp-json/wp/v2/"
            # username 和 password 可以省略，如果该区域 API 不需要身份验证
        """
        self.api_configs = wordpress_api_configs
        self.api_clients: Dict[str, tuple[str, Optional[HTTPBasicAuth]]] = {} # Cache for API clients per region

    def _get_api_client(self, region_code: str) -> tuple[str, Optional[HTTPBasicAuth]]:
        """
        获取指定区域的 WordPress API 客户端配置 (API URL 和身份验证).

        内部方法，用于根据区域代码从配置中获取对应的 API URL 和身份验证信息，并缓存 API 客户端配置，避免重复创建。

        Args:
            region_code (str): 区域代码 (例如 "au", "de", "us").

        Returns:
            tuple[str, Optional[HTTPBasicAuth]]:  包含 API URL (str) 和身份验证对象 (HTTPBasicAuth 或 None) 的元组.
                                                  如果找不到指定区域的配置，则抛出 ValueError 异常.

        Raises:
            ValueError:  如果找不到指定区域代码的 WordPress API 配置.
        """
        if region_code not in self.api_clients:
            region_config = self.api_configs.get(region_code)
            if not region_config:
                raise ValueError(f"No WordPress API configuration found for region code: {region_code}")
            api_url = region_config["api_url"].rstrip('/') + '/' # Ensure API URL ends with a slash
            username = region_config.get("username")
            password = region_config.get("password")
            auth = HTTPBasicAuth(username, password) if username and password else None # Set up authentication if credentials are provided
            self.api_clients[region_code] = (api_url, auth) # Cache API client config
        return self.api_clients[region_code]

    def publish_deal(self, deal_data: dict, region_code: str):
        """
        通过 WordPress REST API 发布 Deal 信息到指定区域的 WordPress 网站 (创建 post_type='deal' 的 WordPress Post) - **已更新，支持多区域**.

        Args:
            deal_data (dict): Deal 数据 (包含 title, content, meta fields 等).
            region_code (str): 目标区域代码 (例如 "au", "de", "us")，指定要发布 Deal 的 WordPress 网站区域.

        Returns:
            dict: WordPress API 响应，通常是 JSON 格式，包含新创建的 WordPress Post 的信息 (例如 ID, link, status).
                  如果 API 请求失败 (例如身份验证失败、数据验证失败、区域配置错误)，则返回包含错误信息的字典或抛出异常 (需要根据实际错误处理逻辑实现).
        """
        api_url, auth = self._get_api_client(region_code) # 获取指定区域的 API 客户端配置
        pass # Implementation to publish deal using requests.post to api_url with auth and deal_data

    def publish_review_article(self, article_data: dict, region_code: str):
        """
        通过 WordPress REST API 发布评测文章到指定区域的 WordPress 网站 (创建 post_type='review_article' 的 WordPress Post) - **已更新，支持多区域**.

        Args:
            article_data (dict): 评测文章数据 (包含 title, content, meta fields 等).
            region_code (str): 目标区域代码.

        Returns:
            dict: WordPress API 响应.
        """
        api_url, auth = self._get_api_client(region_code) # 获取指定区域的 API 客户端配置
        pass # Implementation to publish review article using requests.post to api_url with auth and article_data

    def get_user_comments(self, post_id: int, region_code: str):
        """
        通过 WordPress REST API 获取指定区域 WordPress 文章/Deal 的用户评论列表 - **已更新，支持多区域**.

        Args:
            post_id (int): WordPress 文章/Deal 的 Post ID.
            region_code (str): 目标区域代码.

        Returns:
            list: 用户评论列表.
        """
        api_url, auth = self._get_api_client(region_code) # 获取指定区域的 API 客户端配置
        pass # Implementation to get user comments using requests.get to api_url with auth and post_id

    def get_categories(self, region_code: str):
        """
        通过 WordPress REST API 获取指定区域 WordPress 分类列表 - **已更新，支持多区域**.

        Args:
            region_code (str): 目标区域代码.

        Returns:
            list: WordPress 分类列表.
        """
        api_url, auth = self._get_api_client(region_code) # 获取指定区域的 API 客户端配置
        pass # Implementation to get categories using requests.get to api_url with auth

    def get_tags(self, region_code: str):
        """
        通过 WordPress REST API 获取指定区域 WordPress 标签列表 - **已更新，支持多区域**.

        Args:
            region_code (str): 目标区域代码.

        Returns:
            list: WordPress 标签列表.
        """
        api_url, auth = self._get_api_client(region_code) # 获取指定区域的 API 客户端配置
        pass # Implementation to get tags using requests.get to api_url with auth

    # 其他 WordPress Util 的相关方法 (例如 获取用户信息, 更新文章, 删除文章, 获取媒体库文件等) - 均需更新以支持多区域
    # ...

def create_wordpress_util(wordpress_api_configs: Dict[str, Dict[str, str]]):
    """
    工厂函数，用于创建 WordPressUtil 实例 - **已更新，支持多区域 WordPress API 配置**.

    Args:
        wordpress_api_configs (Dict[str, Dict[str, str]]): 多区域 WordPress API 配置字典 (参考 WordPressUtil.__init__ 文档).

    Returns:
        WordPressUtil: 创建的 WordPressUtil 实例.
    """
    return WordPressUtil(wordpress_api_configs=wordpress_api_configs)

if __name__ == '__main__':
    # (可选) WordPress Util 的单元测试或示例代码可以放在这里
    pass