# ai-backend-system/utils/db_connector.py
"""
定义 DBConnector 类。

该 Util 负责连接和操作 Supabase 云数据库系统 (包括 PostgreSQL 和 pgvector 扩展)，提供统一的接口与 Supabase 数据库进行交互。
作为 Human-agents block 与 Supabase 数据库 AI运营相关表 交互的桥梁。

核心功能包括：
- 连接 Supabase PostgreSQL 数据库
- 连接 Supabase Vector 数据库 (pgvector 扩展)
- 向指定 Supabase 表插入数据 (INSERT)
- 从指定 Supabase 表获取数据 (SELECT)
- 查询向量数据库，进行向量相似度检索
- 更新 Supabase 表数据 (UPDATE)
- 删除 Supabase 表数据 (DELETE)
- 执行更复杂的 SQL 查询
"""
import os
import json
import logging
from typing import List, Dict, Any, Optional, Union, Tuple
import numpy as np

# Supabase 客户端
from supabase.client import Client, create_client
from supabase.lib.client_options import ClientOptions
from supabase import PostgrestFilterBuilder

# Langchain 集成
from langchain_community.vectorstores.supabase import SupabaseVectorStore
from langchain_core.embeddings import Embeddings
from langchain_core.documents import Document

# 自定义异常
class DBConnectorError(Exception):
    """DBConnector 相关异常的基类"""
    pass

class ConnectionError(DBConnectorError):
    """数据库连接错误"""
    pass

class QueryError(DBConnectorError):
    """查询执行错误"""
    pass

class InsertError(DBConnectorError):
    """数据插入错误"""
    pass

class UpdateError(DBConnectorError):
    """数据更新错误"""
    pass

class DeleteError(DBConnectorError):
    """数据删除错误"""
    pass

class VectorOperationError(DBConnectorError):
    """向量操作错误"""
    pass

class DBConnector:
    """
    DBConnector, 负责连接和操作数据库系统 (Supabase - AI运营相关表).
    作为 Human-agents block 与 Supabase 数据库 AI运营相关表 交互的桥梁.
    """
    def __init__(self, supabase_url: str, supabase_key: str, embedding_model: Optional[Embeddings] = None):
        """
        初始化 DBConnector 实例。

        Args:
            supabase_url (str): Supabase 项目 URL，例如 "https://your-project-id.supabase.co"。
            supabase_key (str): Supabase API 密钥 (Service Role 密钥，用于后端服务访问数据库)。 需要妥善保管，避免泄露。
            embedding_model (Optional[Embeddings]): 用于生成向量嵌入的模型。如果提供，将用于向量数据库操作。
        
        Raises:
            ConnectionError: 连接 Supabase 数据库失败时抛出。
        """
        self.supabase_url = supabase_url
        self.supabase_key = supabase_key
        self.embedding_model = embedding_model
        
        try:
            # 初始化 Supabase 客户端
            options = ClientOptions(schema="public")
            self.supabase_client: Client = create_client(supabase_url, supabase_key, options=options)
            
            # 测试连接
            self.supabase_client.table("_dummy").select("*").limit(1).execute()
            logging.info(f"Successfully connected to Supabase at {supabase_url}")
        except Exception as e:
            logging.error(f"Failed to connect to Supabase: {str(e)}")
            raise ConnectionError(f"Failed to connect to Supabase: {str(e)}")
    
    # ==================== 结构化数据写入接口 ====================
    
    def insert_data(self, table_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        向指定的 Supabase 表插入单条数据。

        该方法接收表名 (table_name) 和要插入的数据 (data) 字典作为输入，使用 Supabase 客户端执行 INSERT 操作，将数据插入到指定的 Supabase 表中。

        Args:
            table_name (str): 要插入数据的 Supabase 表名，例如 "products", "deals", "agent_logs" 等。
            data (Dict[str, Any]): 要插入的数据，以字典形式表示，字典的键为表字段名，值为字段值。

        Returns:
            Dict[str, Any]: Supabase API 响应，通常是 JSON 格式，包含操作结果和插入的数据。
                             如果插入成功，可能返回包含插入记录 ID 的信息。

        Raises:
            InsertError: 插入数据失败时抛出，例如数据验证失败、数据库错误等。
        """
        try:
            # 执行插入操作
            response = self.supabase_client.table(table_name).insert(data).execute()
            
            # 检查响应
            if hasattr(response, 'error') and response.error is not None:
                raise InsertError(f"Failed to insert data into {table_name}: {response.error}")
            
            logging.info(f"Successfully inserted data into {table_name}")
            return response.data[0] if response.data else {}
        
        except Exception as e:
            if isinstance(e, InsertError):
                raise
            logging.error(f"Error inserting data into {table_name}: {str(e)}")
            raise InsertError(f"Failed to insert data into {table_name}: {str(e)}")
    
    def batch_insert_data(self, table_name: str, data_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        向指定的 Supabase 表批量插入多条数据。

        该方法接收表名 (table_name) 和要插入的数据列表 (data_list) 作为输入，使用 Supabase 客户端执行批量 INSERT 操作，
        将多条数据一次性插入到指定的 Supabase 表中，提高插入效率。

        Args:
            table_name (str): 要插入数据的 Supabase 表名。
            data_list (List[Dict[str, Any]]): 要插入的数据列表，每个元素是一个字典，代表一条记录。

        Returns:
            List[Dict[str, Any]]: Supabase API 响应，包含所有插入记录的信息。

        Raises:
            InsertError: 批量插入数据失败时抛出。
        """
        if not data_list:
            logging.warning(f"Empty data list provided for batch insert into {table_name}")
            return []
        
        try:
            # 执行批量插入操作
            response = self.supabase_client.table(table_name).insert(data_list).execute()
            
            # 检查响应
            if hasattr(response, 'error') and response.error is not None:
                raise InsertError(f"Failed to batch insert data into {table_name}: {response.error}")
            
            logging.info(f"Successfully batch inserted {len(data_list)} records into {table_name}")
            return response.data
        
        except Exception as e:
            if isinstance(e, InsertError):
                raise
            logging.error(f"Error batch inserting data into {table_name}: {str(e)}")
            raise InsertError(f"Failed to batch insert data into {table_name}: {str(e)}")
    
    # ==================== 结构化数据查询接口 ====================
    
    def fetch_data(self, table_name: str, query_params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        从指定的 Supabase 表获取数据，支持通过 query_params 参数进行条件查询、排序、分页等操作。

        该方法接收表名 (table_name) 和查询参数 (query_params) 字典作为输入，使用 Supabase 客户端执行 SELECT 操作，从指定的 Supabase 表中获取数据。
        query_params 可以用于指定查询条件 (例如 WHERE 子句)、排序规则 (ORDER BY 子句)、分页参数 (limit, offset) 等。

        Args:
            table_name (str): 要查询数据的 Supabase 表名。
            query_params (Dict[str, Any], optional): 查询参数字典，用于构建查询条件。可以包含以下键：
                - 'select':  指定要查询的字段，例如 "id, product_name, price"。 默认为 "*" (查询所有字段)。
                - 'eq':      等于条件，例如 {'product_id': 123} 表示 product_id 等于 123 的记录。
                - 'gt':      大于条件，例如 {'price': 99.99} 表示 price 大于 99.99 的记录。
                - 'lt':      小于条件，例如 {'price': 199.99} 表示 price 小于 199.99 的记录。
                - 'gte':     大于等于条件。
                - 'lte':     小于等于条件。
                - 'like':    模糊匹配条件，例如 {'product_name': '%keyword%'}。
                - 'order':   排序规则，例如 {'column': 'price', 'ascending': False} 表示按 price 字段降序排序。
                - 'limit':   限制返回结果数量，例如 10 表示最多返回 10 条记录。
                - 'offset':  查询结果偏移量，用于分页，例如 20 表示从第 21 条记录开始返回。
                更多查询参数可以参考 Supabase 官方文档。 默认为 None，表示无条件查询，返回所有记录 (受表记录数量限制)。

        Returns:
            List[Dict[str, Any]]: 从数据库获取的数据列表，每个元素是一个字典，代表一条记录，字典的键为表字段名，值为字段值。

        Raises:
            QueryError: 查询数据失败时抛出，例如表不存在、查询参数错误、数据库错误等。
        """
        try:
            # 开始构建查询
            query = self.supabase_client.table(table_name)
            
            # 如果没有查询参数，直接返回所有记录
            if not query_params:
                response = query.select('*').execute()
                return response.data
            
            # 处理 select 参数
            select_fields = query_params.get('select', '*')
            query = query.select(select_fields)
            
            # 处理过滤条件
            if 'eq' in query_params:
                for column, value in query_params['eq'].items():
                    query = query.eq(column, value)
            
            if 'gt' in query_params:
                for column, value in query_params['gt'].items():
                    query = query.gt(column, value)
            
            if 'lt' in query_params:
                for column, value in query_params['lt'].items():
                    query = query.lt(column, value)
            
            if 'gte' in query_params:
                for column, value in query_params['gte'].items():
                    query = query.gte(column, value)
            
            if 'lte' in query_params:
                for column, value in query_params['lte'].items():
                    query = query.lte(column, value)
            
            if 'like' in query_params:
                for column, pattern in query_params['like'].items():
                    query = query.like(column, pattern)
            
            # 处理排序
            if 'order' in query_params:
                order_info = query_params['order']
                column = order_info.get('column')
                ascending = order_info.get('ascending', True)
                if column:
                    query = query.order(column, ascending=ascending)
            
            # 处理分页
            if 'limit' in query_params:
                query = query.limit(query_params['limit'])
            
            if 'offset' in query_params:
                query = query.offset(query_params['offset'])
            
            # 执行查询
            response = query.execute()
            
            # 检查响应
            if hasattr(response, 'error') and response.error is not None:
                raise QueryError(f"Failed to fetch data from {table_name}: {response.error}")
            
            logging.info(f"Successfully fetched {len(response.data)} records from {table_name}")
            return response.data
        
        except Exception as e:
            if isinstance(e, QueryError):
                raise
            logging.error(f"Error fetching data from {table_name}: {str(e)}")
            raise QueryError(f"Failed to fetch data from {table_name}: {str(e)}")
    
    def execute_sql(self, sql: str, params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        执行自定义 SQL 查询。

        该方法允许执行更复杂的 SQL 查询，超出 Supabase 客户端 API 的功能范围。
        支持参数化查询，以防止 SQL 注入攻击。

        Args:
            sql (str): 要执行的 SQL 查询语句。
            params (Dict[str, Any], optional): SQL 查询参数，用于参数化查询。默认为 None。

        Returns:
            List[Dict[str, Any]]: 查询结果列表，每个元素是一个字典，代表一条记录。

        Raises:
            QueryError: 执行 SQL 查询失败时抛出。
        """
        try:
            # 执行 SQL 查询
            response = self.supabase_client.rpc('execute_sql', {'query': sql, 'params': params or {}}).execute()
            
            # 检查响应
            if hasattr(response, 'error') and response.error is not None:
                raise QueryError(f"Failed to execute SQL query: {response.error}")
            
            logging.info(f"Successfully executed SQL query")
            return response.data
        
        except Exception as e:
            if isinstance(e, QueryError):
                raise
            logging.error(f"Error executing SQL query: {str(e)}")
            raise QueryError(f"Failed to execute SQL query: {str(e)}")
    
    # ==================== 数据更新和删除接口 ====================
    
    def update_data(self, table_name: str, filter_params: Dict[str, Any], update_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新指定 Supabase 表中符合条件的数据。

        该方法接收表名 (table_name)、过滤条件 (filter_params) 和要更新的数据 (update_data) 作为输入，
        使用 Supabase 客户端执行 UPDATE 操作，更新指定表中符合条件的记录。

        Args:
            table_name (str): 要更新数据的 Supabase 表名。
            filter_params (Dict[str, Any]): 过滤条件，用于确定要更新哪些记录。
            update_data (Dict[str, Any]): 要更新的数据，以字典形式表示，字典的键为表字段名，值为新的字段值。

        Returns:
            Dict[str, Any]: Supabase API 响应，包含更新操作的结果。

        Raises:
            UpdateError: 更新数据失败时抛出。
        """
        try:
            # 构建查询
            query = self.supabase_client.table(table_name)
            
            # 应用过滤条件
            for column, value in filter_params.items():
                query = query.eq(column, value)
            
            # 执行更新操作
            response = query.update(update_data).execute()
            
            # 检查响应
            if hasattr(response, 'error') and response.error is not None:
                raise UpdateError(f"Failed to update data in {table_name}: {response.error}")
            
            logging.info(f"Successfully updated data in {table_name}")
            return response.data
        
        except Exception as e:
            if isinstance(e, UpdateError):
                raise
            logging.error(f"Error updating data in {table_name}: {str(e)}")
            raise UpdateError(f"Failed to update data in {table_name}: {str(e)}")
    
    def delete_data(self, table_name: str, filter_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        删除指定 Supabase 表中符合条件的数据。

        该方法接收表名 (table_name) 和过滤条件 (filter_params) 作为输入，
        使用 Supabase 客户端执行 DELETE 操作，删除指定表中符合条件的记录。

        Args:
            table_name (str): 要删除数据的 Supabase 表名。
            filter_params (Dict[str, Any]): 过滤条件，用于确定要删除哪些记录。

        Returns:
            Dict[str, Any]: Supabase API 响应，包含删除操作的结果。

        Raises:
            DeleteError: 删除数据失败时抛出。
        """
        try:
            # 构建查询
            query = self.supabase_client.table(table_name)
            
            # 应用过滤条件
            for column, value in filter_params.items():
                query = query.eq(column, value)
            
            # 执行删除操作
            response = query.delete().execute()
            
            # 检查响应
            if hasattr(response, 'error') and response.error is not None:
                raise DeleteError(f"Failed to delete data from {table_name}: {response.error}")
            
            logging.info(f"Successfully deleted data from {table_name}")
            return response.data
        
        except Exception as e:
            if isinstance(e, DeleteError):
                raise
            logging.error(f"Error deleting data from {table_name}: {str(e)}")
            raise DeleteError(f"Failed to delete data from {table_name}: {str(e)}")
    
    # ==================== 向量数据库接口 ====================
    
    def insert_vector_data(self, table_name: str, texts: List[str], metadata: List[Dict[str, Any]], 
                          embedding_column: str = "embedding") -> List[Dict[str, Any]]:
        """
        向向量数据库插入文本数据，自动生成向量嵌入。

        该方法接收表名 (table_name)、文本列表 (texts) 和元数据列表 (metadata) 作为输入，
        使用嵌入模型为每个文本生成向量嵌入，然后将文本、元数据和向量嵌入一起插入到指定的向量表中。

        Args:
            table_name (str): 要插入数据的向量表名。
            texts (List[str]): 要插入的文本列表。
            metadata (List[Dict[str, Any]]): 与文本对应的元数据列表，每个元素是一个字典。
            embedding_column (str, optional): 存储向量嵌入的列名。默认为 "embedding"。

        Returns:
            List[Dict[str, Any]]: 插入的记录列表。

        Raises:
            VectorOperationError: 向量操作失败时抛出，例如嵌入模型未提供、生成嵌入失败等。
        """
        if not self.embedding_model:
            raise VectorOperationError("Embedding model is required for vector operations")
        
        if len(texts) != len(metadata):
            raise VectorOperationError("The length of texts and metadata must be the same")
        
        try:
            # 生成向量嵌入
            embeddings = self.embedding_model.embed_documents(texts)
            
            # 准备插入数据
            records = []
            for i, (text, meta, embedding) in enumerate(zip(texts, metadata, embeddings)):
                record = {
                    "content": text,
                    embedding_column: embedding,
                    **meta
                }
                records.append(record)
            
            # 批量插入数据
            return self.batch_insert_data(table_name, records)
        
        except Exception as e:
            logging.error(f"Error inserting vector data into {table_name}: {str(e)}")
            raise VectorOperationError(f"Failed to insert vector data: {str(e)}")
    
    def query_vector_db(self, query_embedding: List[float], table_name: str, embedding_column: str = "embedding", 
                       match_threshold: float = 0.8, match_count: int = 10) -> List[Dict[str, Any]]:
        """
        查询向量数据库 (Supabase pgvector)，进行向量相似度检索。

        该方法接收查询向量 (query_embedding)、表名 (table_name)、向量列名 (embedding_column)、相似度匹配阈值 (match_threshold) 和返回结果数量 (match_count) 作为输入，使用 Supabase 客户端和 pgvector 扩展执行向量相似度检索。
        使用 cosine similarity 作为相似度度量方法。

        Args:
            query_embedding (List[float]): 查询向量，通常是一个浮点数列表，由 Embedding 模型生成。
            table_name (str): 向量数据所在的 Supabase 表名，例如 "knowledge_documents"。
            embedding_column (str, optional): 向量数据在表中的列名，例如 "embedding"。默认为 "embedding"。
            match_threshold (float, optional): 相似度匹配阈值，只有相似度评分高于该阈值的结果才会被返回。 范围通常在 0 到 1 之间，值越大表示相似度要求越高。 默认为 0.8。
            match_count (int, optional): 返回最相似的结果数量，限制返回结果列表的长度。 默认为 10。

        Returns:
            List[Dict[str, Any]]: 向量数据库匹配结果列表，每个元素是一个字典，代表一条匹配的记录，包含记录的原始数据以及相似度评分 (_similarity 字段)。
                                  结果按照相似度评分降序排列。

        Raises:
            VectorOperationError: 向量查询操作失败时抛出。
        """
        try:
            # 构建 RPC 调用参数
            rpc_params = {
                'query_embedding': query_embedding,
                'table_name': table_name,
                'embedding_column': embedding_column,
                'match_threshold': match_threshold,
                'match_count': match_count
            }
            
            # 调用 Supabase 存储过程进行向量相似度搜索
            response = self.supabase_client.rpc('match_documents', rpc_params).execute()
            
            # 检查响应
            if hasattr(response, 'error') and response.error is not None:
                raise VectorOperationError(f"Failed to query vector database: {response.error}")
            
            logging.info(f"Successfully queried vector database, found {len(response.data)} matches")
            return response.data
        
        except Exception as e:
            if isinstance(e, VectorOperationError):
                raise
            logging.error(f"Error querying vector database: {str(e)}")
            raise VectorOperationError(f"Failed to query vector database: {str(e)}")
    
    def query_vector_by_text(self, query_text: str, table_name: str, embedding_column: str = "embedding",
                           match_threshold: float = 0.8, match_count: int = 10) -> List[Dict[str, Any]]:
        """
        通过文本查询向量数据库，自动生成查询文本的向量嵌入。

        该方法接收查询文本 (query_text) 和其他参数作为输入，使用嵌入模型为查询文本生成向量嵌入，
        然后调用 query_vector_db 方法进行向量相似度检索。

        Args:
            query_text (str): 查询文本。
            table_name (str): 向量数据所在的 Supabase 表名。
            embedding_column (str, optional): 向量数据在表中的列名。默认为 "embedding"。
            match_threshold (float, optional): 相似度匹配阈值。默认为 0.8。
            match_count (int, optional): 返回最相似的结果数量。默认为 10。

        Returns:
            List[Dict[str, Any]]: 向量数据库匹配结果列表。

        Raises:
            VectorOperationError: 向量查询操作失败时抛出。
        """
        if not self.embedding_model:
            raise VectorOperationError("Embedding model is required for vector operations")
        
        try:
            # 生成查询文本的向量嵌入
            query_embedding = self.embedding_model.embed_query(query_text)
            
            # 调用向量查询方法
            return self.query_vector_db(
                query_embedding=query_embedding,
                table_name=table_name,
                embedding_column=embedding_column,
                match_threshold=match_threshold,
                match_count=match_count
            )
        
        except Exception as e:
            logging.error(f"Error querying vector database by text: {str(e)}")
            raise VectorOperationError(f"Failed to query vector database by text: {str(e)}")
    
    # ==================== Langchain 集成接口 ====================
    
    def get_vector_store(self, table_name: str, embedding_column: str = "embedding", 
                        content_column: str = "content") -> SupabaseVectorStore:
        """
        获取 Langchain SupabaseVectorStore 实例，用于与 Langchain 生态系统集成。

        该方法返回一个配置好的 SupabaseVectorStore 实例，可以直接用于 Langchain 的各种操作，
        如文档检索、问答系统等。

        Args:
            table_name (str): 向量数据所在的 Supabase 表名。
            embedding_column (str, optional): 向量数据在表中的列名。默认为 "embedding"。
            content_column (str, optional): 文本内容在表中的列名。默认为 "content"。

        Returns:
            SupabaseVectorStore: 配置好的 Langchain SupabaseVectorStore 实例。

        Raises:
            VectorOperationError: 创建向量存储实例失败时抛出。
        """
        if not self.embedding_model:
            raise VectorOperationError("Embedding model is required for vector operations")
        
        try:
            # 创建 SupabaseVectorStore 实例
            vector_store = SupabaseVectorStore(
                client=self.supabase_client,
                embedding=self.embedding_model,
                table_name=table_name,
                query_name="match_documents",
                embedding_column=embedding_column,
                content_column=content_column
            )
            
            return vector_store
        
        except Exception as e:
            logging.error(f"Error creating vector store: {str(e)}")
            raise VectorOperationError(f"Failed to create vector store: {str(e)}")
    
    def add_documents_to_vector_store(self, documents: List[Document], table_name: str, 
                                     embedding_column: str = "embedding") -> List[str]:
        """
        将 Langchain Document 对象添加到向量存储中。

        该方法接收 Langchain Document 对象列表和其他参数作为输入，使用 SupabaseVectorStore 将文档添加到向量存储中。

        Args:
            documents (List[Document]): Langchain Document 对象列表。
            table_name (str): 向量数据所在的 Supabase 表名。
            embedding_column (str, optional): 向量数据在表中的列名。默认为 "embedding"。

        Returns:
            List[str]: 添加的文档 ID 列表。

        Raises:
            VectorOperationError: 添加文档失败时抛出。
        """
        try:
            # 获取向量存储实例
            vector_store = self.get_vector_store(table_name, embedding_column)
            
            # 添加文档
            doc_ids = vector_store.add_documents(documents)
            
            logging.info(f"Successfully added {len(documents)} documents to vector store")
            return doc_ids
        
        except Exception as e:
            logging.error(f"Error adding documents to vector store: {str(e)}")
            raise VectorOperationError(f"Failed to add documents to vector store: {str(e)}")
    
    def similarity_search(self, query: str, table_name: str, k: int = 4) -> List[Document]:
        """
        使用 Langchain 接口进行相似度搜索，返回 Document 对象。

        该方法接收查询文本和其他参数作为输入，使用 SupabaseVectorStore 进行相似度搜索，
        返回与查询文本最相似的 Document 对象列表。

        Args:
            query (str): 查询文本。
            table_name (str): 向量数据所在的 Supabase 表名。
            k (int, optional): 返回结果数量。默认为 4。

        Returns:
            List[Document]: 与查询文本最相似的 Document 对象列表。

        Raises:
            VectorOperationError: 相似度搜索失败时抛出。
        """
        try:
            # 获取向量存储实例
            vector_store = self.get_vector_store(table_name)
            
            # 执行相似度搜索
            docs = vector_store.similarity_search(query, k=k)
            
            logging.info(f"Successfully performed similarity search, found {len(docs)} documents")
            return docs
        
        except Exception as e:
            logging.error(f"Error performing similarity search: {str(e)}")
            raise VectorOperationError(f"Failed to perform similarity search: {str(e)}")
    
    # ==================== 辅助方法 ====================
    
    def _format_query_params(self, query_params: Dict[str, Any], query_builder: PostgrestFilterBuilder) -> PostgrestFilterBuilder:
        """
        根据查询参数格式化 Postgrest 查询构建器。

        该方法接收查询参数字典和 Postgrest 查询构建器作为输入，根据查询参数配置查询构建器，
        支持各种查询条件，如等于、大于、小于、模糊匹配等。

        Args:
            query_params (Dict[str, Any]): 查询参数字典。
            query_builder (PostgrestFilterBuilder): Postgrest 查询构建器。

        Returns:
            PostgrestFilterBuilder: 配置好的查询构建器。
        """
        # 处理选择字段
        if 'select' in query_params:
            query_builder = query_builder.select(query_params['select'])
        
        # 处理等于条件
        if 'eq' in query_params:
            for column, value in query_params['eq'].items():
                query_builder = query_builder.eq(column, value)
        
        # 处理大于条件
        if 'gt' in query_params:
            for column, value in query_params['gt'].items():
                query_builder = query_builder.gt(column, value)
        
        # 处理小于条件
        if 'lt' in query_params:
            for column, value in query_params['lt'].items():
                query_builder = query_builder.lt(column, value)
        
        # 处理大于等于条件
        if 'gte' in query_params:
            for column, value in query_params['gte'].items():
                query_builder = query_builder.gte(column, value)
        
        # 处理小于等于条件
        if 'lte' in query_params:
            for column, value in query_params['lte'].items():
                query_builder = query_builder.lte(column, value)
        
        # 处理模糊匹配条件
        if 'like' in query_params:
            for column, value in query_params['like'].items():
                query_builder = query_builder.like(column, value)
        
        # 处理排序
        if 'order' in query_params:
            column = query_params['order'].get('column')
            ascending = query_params['order'].get('ascending', True)
            if column:
                query_builder = query_builder.order(column, ascending=ascending)
        
        # 处理限制
        if 'limit' in query_params:
            query_builder = query_builder.limit(query_params['limit'])
        
        # 处理偏移
        if 'offset' in query_params:
            query_builder = query_builder.offset(query_params['offset'])
        
        return query_builder

def query_similar_deals(self, query_text: str, match_threshold: float = 0.7, match_count: int = 10) -> List[Dict[str, Any]]:
    """
    查询与给定文本相似的交易信息。

    该方法为给定的查询文本生成嵌入向量，然后在 backend_deal_name_embeddings 表中
    搜索最相似的交易记录。这对于"你可能也喜欢"或交易聚类功能非常有用。

    Args:
        query_text (str): 要搜索的查询文本。
        match_threshold (float, optional): 相似度匹配阈值，范围从 0 到 1。默认为 0.7。
        match_count (int, optional): 返回的最大匹配项数量。默认为 10。

    Returns:
        List[Dict[str, Any]]: 与查询文本相似的交易列表，按相似度降序排列。

    Raises:
        VectorOperationError: 向量操作失败时抛出，例如嵌入模型未提供、向量查询失败等。
    """
    if not self.embedding_model:
        raise VectorOperationError("Embedding model is required for vector operations")
    
    try:
        # 生成查询文本的嵌入向量
        query_embedding = self.embedding_model.embed_query(query_text)
        
        # 执行向量相似度查询
        sql = """
        WITH similarity_search AS (
            SELECT
                d.id,
                d.name,
                d.product_id,
                d.date,
                d.img_link,
                d.source_site,
                d.current_price,
                d.original_price,
                d.average_price,
                d.original_link,
                d.deal_link,
                d.description,
                d.categories,
                d.expiration,
                1 - (e.embedding <=> $1) as similarity
            FROM
                backend_deals d
            JOIN
                backend_deal_name_embeddings e ON d.id = e.deal_id
            WHERE
                1 - (e.embedding <=> $1) > $2
            ORDER BY
                similarity DESC
            LIMIT $3
        )
        SELECT
            s.*,
            p.product_name,
            p.brand,
            p.category,
            p.asin
        FROM
            similarity_search s
        LEFT JOIN
            backend_products p ON s.product_id = p.id
        ORDER BY
            s.similarity DESC
        """
        
        # 执行查询
        params = {
            "1": query_embedding,
            "2": match_threshold,
            "3": match_count
        }
        
        result = self.execute_sql(sql, params)
        logging.info(f"Found {len(result)} similar deals for query: {query_text[:50]}...")
        return result
        
    except Exception as e:
        logging.error(f"Error querying similar deals: {str(e)}")
        raise VectorOperationError(f"Failed to query similar deals: {str(e)}")

def insert_deal_with_embedding(self, deal_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    插入交易数据并自动生成交易名称的嵌入向量。

    该方法在单个事务中同时插入交易数据到 backend_deals 表，并为交易名称
    生成嵌入向量存入 backend_deal_name_embeddings 表。

    Args:
        deal_data (Dict[str, Any]): 要插入的交易数据，包括名称、价格、描述等字段。

    Returns:
        Dict[str, Any]: 包含交易 ID 和其他插入结果的字典。

    Raises:
        InsertError: 插入数据失败时抛出。
        VectorOperationError: 向量操作失败时抛出。
    """
    if not self.embedding_model:
        raise VectorOperationError("Embedding model is required for generating embeddings")
    
    try:
        # 开始事务
        sql_begin = "BEGIN;"
        self.execute_sql(sql_begin)
        
        # 插入交易数据
        deal_result = self.insert_data("backend_deals", deal_data)
        deal_id = deal_result.get("id")
        
        if not deal_id:
            raise InsertError("Failed to get deal ID after insertion")
        
        # 生成交易名称的嵌入向量
        deal_name = deal_data.get("name", "")
        if deal_name:
            embedding = self.embedding_model.embed_query(deal_name)
            
            # 插入嵌入向量
            embedding_data = {
                "deal_id": deal_id,
                "embedding": embedding
            }
            self.insert_data("backend_deal_name_embeddings", embedding_data)
        
        # 提交事务
        sql_commit = "COMMIT;"
        self.execute_sql(sql_commit)
        
        logging.info(f"Successfully inserted deal with ID {deal_id} and generated embedding")
        return deal_result
        
    except Exception as e:
        # 回滚事务
        sql_rollback = "ROLLBACK;"
        self.execute_sql(sql_rollback)
        
        logging.error(f"Error inserting deal with embedding: {str(e)}")
        if isinstance(e, (InsertError, VectorOperationError)):
            raise
        raise InsertError(f"Failed to insert deal with embedding: {str(e)}")

def get_deals_by_product_id(self, product_id: int) -> List[Dict[str, Any]]:
    """
    获取与指定产品 ID 关联的所有交易。

    Args:
        product_id (int): 产品 ID

    Returns:
        List[Dict[str, Any]]: 关联的交易列表

    Raises:
        QueryError: 查询失败时抛出
    """
    try:
        query_params = {
            'eq': {'product_id': product_id}
        }
        return self.fetch_data("backend_deals", query_params)
    except Exception as e:
        logging.error(f"Error fetching deals for product ID {product_id}: {str(e)}")
        raise QueryError(f"Failed to fetch deals for product ID {product_id}: {str(e)}")


def create_db_connector(supabase_url: str, supabase_key: str, embedding_model: Optional[Embeddings] = None) -> DBConnector:
    """
    工厂函数，用于创建 DBConnector 实例。

    Args:
        supabase_url (str): Supabase 项目 URL.
        supabase_key (str): Supabase API 密钥.
        embedding_model (Optional[Embeddings]): 用于生成向量嵌入的模型。

    Returns:
        DBConnector: 创建的 DBConnector 实例.
    """
    return DBConnector(supabase_url=supabase_url, supabase_key=supabase_key, embedding_model=embedding_model)


if __name__ == '__main__':
    # 示例代码
    pass