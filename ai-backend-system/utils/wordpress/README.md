# WordPress API Wrapper for AutoGen Agents

A comprehensive WordPress REST API wrapper designed specifically for AutoGen agents, providing async/await operations, multi-region support, and seamless integration with the AI backend system.

## Features

- **Multi-region WordPress site support** - Manage content across different geographical regions
- **Async/await operations** - Full asynchronous support for AutoGen v0.4+ agents
- **Comprehensive post management** - Create, read, update, delete posts with categories, tags, and media
- **Comment management and moderation** - Handle user comments with approval workflows
- **Media upload and optimization** - Upload images, videos, documents with automatic optimization
- **Multiple authentication methods** - Basic Auth, Application Passwords, JWT, OAuth support
- **Rate limiting and retry logic** - Built-in protection against API limits
- **Monitoring and logging** - Comprehensive metrics and error tracking
- **Batch operations** - Efficient bulk operations for content management

## Quick Start

### Basic Usage

```python
from utils.wordpress import WordPressClient

# Create client for a specific region
async with WordPressClient(region_code='au') as client:
    # Create a new post
    post = await client.create_post(
        title="AI-Generated Article",
        content="<p>This content was created by an AutoGen agent.</p>",
        status="published",
        categories=["Technology", "AI"],
        tags=["automation", "wordpress"]
    )
    
    print(f"Created post with ID: {post['id']}")
```

### Media Upload

```python
# Upload and optimize an image
media_handler = client.get_media_handler()

# Upload from file
media = await media_handler.upload_from_file(
    "path/to/image.jpg",
    title="Featured Image",
    alt_text="AI-generated content image"
)

# Set as featured image for a post
await media_handler.set_featured_image(post['id'], media['id'])
```

### Comment Management

```python
# Create a comment
comment = await client.create_comment(
    post_id=post['id'],
    content="Great article! Thanks for sharing.",
    author_name="AI Assistant",
    author_email="<EMAIL>"
)

# Moderate comments
await client.moderate_comment(comment['id'], action='approved')

# Get comments for a post
comments = await client.get_comments(post_id=post['id'], status='approved')
```

## Configuration

### WordPress API Configuration

Add your WordPress sites to `ai-backend-system/config/config.yaml`:

```yaml
wordpress_apis:
  au:  # Australia region
    api_url: https://au.yoursite.com/wp-json/wp/v2/
    username: your_username
    password: your_password  # or application password
    auth_method: basic  # basic, app_password, jwt, oauth
    timeout: 30
    max_retries: 3
    retry_delay: 1.0
  
  us:  # US region
    api_url: https://us.yoursite.com/wp-json/wp/v2/
    username: us_username
    password: us_password
    auth_method: app_password
```

### WordPress Wrapper Settings

```yaml
wordpress:
  # Rate limiting
  rate_limiting:
    requests_per_minute: 60
    burst_limit: 10
  
  # Media settings
  media:
    max_file_size_mb: 50
    supported_image_formats: [jpg, jpeg, png, gif, webp]
    image_optimization:
      enabled: true
      max_width: 1920
      max_height: 1080
      quality: 85
  
  # Default post settings
  posts:
    default_status: draft
    default_comment_status: open
  
  # Comment settings
  comments:
    default_status: hold
    moderation_required: true
  
  # Batch operation limits
  batch:
    max_posts_per_batch: 20
    max_comments_per_batch: 50
    max_media_per_batch: 10
```

## Authentication Methods

### Basic Authentication

```yaml
auth_method: basic
username: your_username
password: your_password
```

### Application Passwords (Recommended)

```yaml
auth_method: app_password
username: your_username
password: your_application_password  # Generated in WordPress admin
```

### JWT Authentication

```yaml
auth_method: jwt
username: your_username
password: your_password
jwt_endpoint: https://yoursite.com/wp-json/jwt-auth/v1/token
```

## Advanced Usage

### Batch Operations

```python
# Create multiple posts at once
posts_data = [
    {
        'title': 'Post 1',
        'content': 'Content for post 1',
        'categories': ['Tech']
    },
    {
        'title': 'Post 2', 
        'content': 'Content for post 2',
        'categories': ['News']
    }
]

results = await client.create_posts_batch(posts_data)

# Moderate multiple comments
comment_ids = [1, 2, 3, 4, 5]
await client.moderate_comments_batch(comment_ids, action='approved')
```

### Custom Post Types and Meta Fields

```python
# Create post with custom meta fields
post = await client.create_post(
    title="Product Review",
    content="Detailed product review content...",
    meta={
        'product_rating': '4.5',
        'product_price': '$99.99',
        'review_date': '2024-01-15'
    }
)
```

### Error Handling

```python
from utils.wordpress import (
    WordPressError,
    AuthenticationError,
    RateLimitError,
    PostError,
    CommentError,
    MediaUploadError
)

try:
    post = await client.create_post(title="Test", content="Test content")
except AuthenticationError:
    print("WordPress authentication failed")
except RateLimitError as e:
    print(f"Rate limited. Retry after {e.retry_after} seconds")
except PostError as e:
    print(f"Post creation failed: {e}")
except WordPressError as e:
    print(f"General WordPress error: {e}")
```

## Integration with AutoGen Agents

### Content Generation Agent Example

```python
from autogen_agentchat.agents import AssistantAgent
from utils.wordpress import WordPressClient

class ContentPublisherAgent(AssistantAgent):
    def __init__(self, region_code='au'):
        super().__init__(
            name="content_publisher",
            description="Publishes AI-generated content to WordPress"
        )
        self.region_code = region_code
    
    async def publish_article(self, title: str, content: str, categories: list):
        """Publish an article to WordPress."""
        async with WordPressClient(region_code=self.region_code) as client:
            # Create the post
            post = await client.create_post(
                title=title,
                content=content,
                categories=categories,
                status='draft'  # Start as draft for review
            )
            
            return {
                'post_id': post['id'],
                'post_url': post['link'],
                'status': 'published'
            }
```

### Comment Moderation Agent Example

```python
class CommentModeratorAgent(AssistantAgent):
    def __init__(self, region_code='au'):
        super().__init__(
            name="comment_moderator",
            description="Moderates WordPress comments using AI"
        )
        self.region_code = region_code
    
    async def moderate_pending_comments(self):
        """Review and moderate pending comments."""
        async with WordPressClient(region_code=self.region_code) as client:
            # Get pending comments
            pending_comments = await client.get_comments(status='hold')
            
            moderated_count = 0
            for comment in pending_comments:
                # AI-based content analysis (implement your logic)
                if self._is_spam(comment['content']['rendered']):
                    await client.moderate_comment(comment['id'], 'spam')
                elif self._is_appropriate(comment['content']['rendered']):
                    await client.moderate_comment(comment['id'], 'approved')
                
                moderated_count += 1
            
            return f"Moderated {moderated_count} comments"
    
    def _is_spam(self, content: str) -> bool:
        # Implement spam detection logic
        spam_keywords = ['viagra', 'casino', 'lottery']
        return any(keyword in content.lower() for keyword in spam_keywords)
    
    def _is_appropriate(self, content: str) -> bool:
        # Implement content appropriateness check
        return len(content) > 10 and not self._contains_profanity(content)
```

## Monitoring and Metrics

The wrapper includes comprehensive monitoring capabilities:

```python
# Get metrics for a specific region
from utils.wordpress.monitoring import get_monitor

monitor = get_monitor('au')
metrics = monitor.get_metrics_summary()

print(f"Total requests: {metrics['requests']['total']}")
print(f"Success rate: {metrics['requests']['success_rate']:.1f}%")
print(f"Average response time: {metrics['performance']['avg_response_time']:.3f}s")
print(f"Posts created: {metrics['operations']['posts_created']}")
print(f"Media uploaded: {metrics['operations']['media_uploaded']}")
```

## Testing

Run the comprehensive test suite:

```bash
# Run all WordPress wrapper tests
pytest ai-backend-system/tests/test_wordpress_wrapper.py -v

# Run specific test categories
pytest ai-backend-system/tests/test_wordpress_wrapper.py::TestPostManagement -v
pytest ai-backend-system/tests/test_wordpress_wrapper.py::TestAuthentication -v
pytest ai-backend-system/tests/test_wordpress_wrapper.py::TestMediaHandler -v
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify username/password or application password
   - Check WordPress user permissions
   - Ensure REST API is enabled

2. **Rate Limiting**
   - Adjust `requests_per_minute` in configuration
   - Implement exponential backoff in your agents
   - Consider using multiple API users

3. **Media Upload Failures**
   - Check file size limits
   - Verify supported file formats
   - Ensure WordPress media upload permissions

4. **Connection Timeouts**
   - Increase `timeout` value in configuration
   - Check network connectivity
   - Verify WordPress site accessibility

### Debug Mode

Enable detailed logging for debugging:

```yaml
wordpress:
  logging:
    log_api_requests: true
    log_api_responses: true  # Enable for debugging only
    log_media_uploads: true
    log_authentication: true
```

## API Reference

For detailed API documentation, see the docstrings in:
- `wordpress_client.py` - Main client methods
- `media_handler.py` - Media upload and management
- `auth.py` - Authentication providers
- `monitoring.py` - Metrics and monitoring

## Contributing

When extending the WordPress wrapper:

1. Follow the existing async/await patterns
2. Add comprehensive error handling
3. Include monitoring calls for new operations
4. Write unit tests with mocked API calls
5. Update configuration schema if needed
6. Follow the existing logging standards

## License

This WordPress API wrapper is part of the AI Backend System and follows the same licensing terms.
