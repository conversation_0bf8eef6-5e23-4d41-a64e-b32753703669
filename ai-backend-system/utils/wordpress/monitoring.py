# ai-backend-system/utils/wordpress/monitoring.py
"""
WordPress API monitoring and metrics collection.

This module provides monitoring capabilities for WordPress API operations,
including performance metrics, error tracking, and usage statistics.

Features:
- API request/response time tracking
- Error rate monitoring
- Rate limiting metrics
- Authentication success/failure tracking
- Media upload statistics
- Integration with existing logging system
"""

import logging
import time
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
import json

from config import get_config


@dataclass
class APIMetrics:
    """Container for API operation metrics."""
    
    # Request metrics
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    
    # Response time metrics
    response_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    avg_response_time: float = 0.0
    max_response_time: float = 0.0
    min_response_time: float = float('inf')
    
    # Error metrics
    error_counts: Dict[str, int] = field(default_factory=lambda: defaultdict(int))
    rate_limit_hits: int = 0
    auth_failures: int = 0
    
    # Operation-specific metrics
    posts_created: int = 0
    posts_updated: int = 0
    posts_deleted: int = 0
    comments_created: int = 0
    comments_moderated: int = 0
    media_uploaded: int = 0
    media_upload_size_mb: float = 0.0
    
    # Time tracking
    start_time: datetime = field(default_factory=datetime.now)
    last_request_time: Optional[datetime] = None


class WordPressMonitor:
    """
    Monitors WordPress API operations and collects metrics.
    
    Provides comprehensive monitoring of WordPress API usage including:
    - Performance metrics and response times
    - Error tracking and categorization
    - Rate limiting and authentication monitoring
    - Operation-specific statistics
    """
    
    def __init__(self, region_code: str = None):
        """
        Initialize WordPress monitor.
        
        Args:
            region_code (str): Region code for multi-region monitoring
        """
        self.region_code = region_code or 'default'
        self.logger = logging.getLogger(f"{__name__}.{self.region_code}")
        
        # Initialize metrics
        self.metrics = APIMetrics()
        
        # Load monitoring configuration
        self._load_config()
        
        # Setup specialized loggers
        self._setup_loggers()
    
    def _load_config(self):
        """Load monitoring configuration."""
        try:
            config = get_config()
            self.config = config.get('wordpress', {}).get('logging', {})
            
            # Default monitoring settings
            self.log_api_requests = self.config.get('log_api_requests', True)
            self.log_api_responses = self.config.get('log_api_responses', False)
            self.log_media_uploads = self.config.get('log_media_uploads', True)
            self.log_authentication = self.config.get('log_authentication', True)
            
        except Exception as e:
            self.logger.warning(f"Failed to load monitoring config: {str(e)}")
            # Use defaults
            self.log_api_requests = True
            self.log_api_responses = False
            self.log_media_uploads = True
            self.log_authentication = True
    
    def _setup_loggers(self):
        """Setup specialized loggers for different operation types."""
        # API operations logger
        self.api_logger = logging.getLogger(f"wordpress.api.{self.region_code}")
        
        # Authentication logger
        self.auth_logger = logging.getLogger(f"wordpress.auth.{self.region_code}")
        
        # Media operations logger
        self.media_logger = logging.getLogger(f"wordpress.media.{self.region_code}")
        
        # Error logger
        self.error_logger = logging.getLogger(f"wordpress.errors.{self.region_code}")
    
    def start_request(self, method: str, endpoint: str, **kwargs) -> str:
        """
        Start tracking an API request.
        
        Args:
            method (str): HTTP method
            endpoint (str): API endpoint
            **kwargs: Additional request parameters
            
        Returns:
            str: Request tracking ID
        """
        request_id = f"{method}_{endpoint}_{int(time.time() * 1000)}"
        
        if self.log_api_requests:
            self.api_logger.info(
                f"API Request Started - ID: {request_id}, Method: {method}, "
                f"Endpoint: {endpoint}, Region: {self.region_code}"
            )
        
        # Store request start time
        setattr(self, f"_start_time_{request_id}", time.time())
        
        return request_id
    
    def end_request(
        self,
        request_id: str,
        status_code: int,
        response_data: Dict[str, Any] = None,
        error: Exception = None
    ):
        """
        End tracking an API request and record metrics.
        
        Args:
            request_id (str): Request tracking ID
            status_code (int): HTTP status code
            response_data (Dict[str, Any]): Response data
            error (Exception): Exception if request failed
        """
        # Calculate response time
        start_time_attr = f"_start_time_{request_id}"
        start_time = getattr(self, start_time_attr, time.time())
        response_time = time.time() - start_time
        
        # Clean up start time
        if hasattr(self, start_time_attr):
            delattr(self, start_time_attr)
        
        # Update metrics
        self.metrics.total_requests += 1
        self.metrics.last_request_time = datetime.now()
        
        # Update response time metrics
        self.metrics.response_times.append(response_time)
        self.metrics.avg_response_time = sum(self.metrics.response_times) / len(self.metrics.response_times)
        self.metrics.max_response_time = max(self.metrics.max_response_time, response_time)
        self.metrics.min_response_time = min(self.metrics.min_response_time, response_time)
        
        # Track success/failure
        if status_code < 400 and not error:
            self.metrics.successful_requests += 1
            
            if self.log_api_requests:
                self.api_logger.info(
                    f"API Request Completed - ID: {request_id}, Status: {status_code}, "
                    f"Response Time: {response_time:.3f}s"
                )
        else:
            self.metrics.failed_requests += 1
            
            # Track specific error types
            if status_code == 429:
                self.metrics.rate_limit_hits += 1
            elif status_code == 401:
                self.metrics.auth_failures += 1
            
            if error:
                error_type = type(error).__name__
                self.metrics.error_counts[error_type] += 1
            
            # Log error
            self.error_logger.error(
                f"API Request Failed - ID: {request_id}, Status: {status_code}, "
                f"Error: {str(error) if error else 'HTTP Error'}, "
                f"Response Time: {response_time:.3f}s"
            )
        
        # Log response data if enabled
        if self.log_api_responses and response_data:
            self.api_logger.debug(f"API Response - ID: {request_id}, Data: {json.dumps(response_data)}")
    
    def track_post_operation(self, operation: str, post_id: int = None, **kwargs):
        """
        Track post-related operations.
        
        Args:
            operation (str): Operation type (create, update, delete)
            post_id (int): Post ID
            **kwargs: Additional operation data
        """
        if operation == 'create':
            self.metrics.posts_created += 1
        elif operation == 'update':
            self.metrics.posts_updated += 1
        elif operation == 'delete':
            self.metrics.posts_deleted += 1
        
        self.api_logger.info(
            f"Post Operation - Type: {operation}, Post ID: {post_id}, "
            f"Region: {self.region_code}"
        )
    
    def track_comment_operation(self, operation: str, comment_id: int = None, **kwargs):
        """
        Track comment-related operations.
        
        Args:
            operation (str): Operation type (create, moderate, delete)
            comment_id (int): Comment ID
            **kwargs: Additional operation data
        """
        if operation == 'create':
            self.metrics.comments_created += 1
        elif operation == 'moderate':
            self.metrics.comments_moderated += 1
        
        self.api_logger.info(
            f"Comment Operation - Type: {operation}, Comment ID: {comment_id}, "
            f"Region: {self.region_code}"
        )
    
    def track_media_upload(self, filename: str, file_size_mb: float, media_id: int = None, **kwargs):
        """
        Track media upload operations.
        
        Args:
            filename (str): Uploaded filename
            file_size_mb (float): File size in MB
            media_id (int): WordPress media ID
            **kwargs: Additional upload data
        """
        self.metrics.media_uploaded += 1
        self.metrics.media_upload_size_mb += file_size_mb
        
        if self.log_media_uploads:
            self.media_logger.info(
                f"Media Upload - File: {filename}, Size: {file_size_mb:.2f}MB, "
                f"Media ID: {media_id}, Region: {self.region_code}"
            )
    
    def track_authentication(self, auth_method: str, success: bool, **kwargs):
        """
        Track authentication operations.
        
        Args:
            auth_method (str): Authentication method used
            success (bool): Whether authentication succeeded
            **kwargs: Additional auth data
        """
        if self.log_authentication:
            status = "Success" if success else "Failed"
            self.auth_logger.info(
                f"Authentication - Method: {auth_method}, Status: {status}, "
                f"Region: {self.region_code}"
            )
        
        if not success:
            self.metrics.auth_failures += 1
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive metrics summary.
        
        Returns:
            Dict[str, Any]: Metrics summary
        """
        uptime = datetime.now() - self.metrics.start_time
        
        return {
            'region': self.region_code,
            'uptime_seconds': uptime.total_seconds(),
            'requests': {
                'total': self.metrics.total_requests,
                'successful': self.metrics.successful_requests,
                'failed': self.metrics.failed_requests,
                'success_rate': (
                    self.metrics.successful_requests / self.metrics.total_requests * 100
                    if self.metrics.total_requests > 0 else 0
                )
            },
            'performance': {
                'avg_response_time': self.metrics.avg_response_time,
                'max_response_time': self.metrics.max_response_time,
                'min_response_time': self.metrics.min_response_time if self.metrics.min_response_time != float('inf') else 0
            },
            'errors': {
                'rate_limit_hits': self.metrics.rate_limit_hits,
                'auth_failures': self.metrics.auth_failures,
                'error_counts': dict(self.metrics.error_counts)
            },
            'operations': {
                'posts_created': self.metrics.posts_created,
                'posts_updated': self.metrics.posts_updated,
                'posts_deleted': self.metrics.posts_deleted,
                'comments_created': self.metrics.comments_created,
                'comments_moderated': self.metrics.comments_moderated,
                'media_uploaded': self.metrics.media_uploaded,
                'media_upload_size_mb': self.metrics.media_upload_size_mb
            },
            'last_request_time': self.metrics.last_request_time.isoformat() if self.metrics.last_request_time else None
        }
    
    def log_metrics_summary(self):
        """Log comprehensive metrics summary."""
        summary = self.get_metrics_summary()
        
        self.logger.info(
            f"WordPress API Metrics Summary for {self.region_code}:\n"
            f"  Uptime: {summary['uptime_seconds']:.0f}s\n"
            f"  Requests: {summary['requests']['total']} "
            f"({summary['requests']['success_rate']:.1f}% success)\n"
            f"  Avg Response Time: {summary['performance']['avg_response_time']:.3f}s\n"
            f"  Posts Created: {summary['operations']['posts_created']}\n"
            f"  Media Uploaded: {summary['operations']['media_uploaded']} "
            f"({summary['operations']['media_upload_size_mb']:.1f}MB)\n"
            f"  Rate Limit Hits: {summary['errors']['rate_limit_hits']}\n"
            f"  Auth Failures: {summary['errors']['auth_failures']}"
        )
    
    def reset_metrics(self):
        """Reset all metrics to initial state."""
        self.metrics = APIMetrics()
        self.logger.info(f"Metrics reset for region: {self.region_code}")


# Global monitor instances for different regions
_monitors: Dict[str, WordPressMonitor] = {}


def get_monitor(region_code: str = 'default') -> WordPressMonitor:
    """
    Get or create a monitor instance for a specific region.
    
    Args:
        region_code (str): Region code
        
    Returns:
        WordPressMonitor: Monitor instance
    """
    if region_code not in _monitors:
        _monitors[region_code] = WordPressMonitor(region_code)
    
    return _monitors[region_code]


def log_all_metrics():
    """Log metrics summary for all active monitors."""
    for region_code, monitor in _monitors.items():
        monitor.log_metrics_summary()
