# ai-backend-system/utils/wordpress/exceptions.py
"""
WordPress API wrapper custom exceptions.

This module defines all custom exceptions used by the WordPress API wrapper,
following the existing error handling patterns in the AI backend system.
"""


class WordPressError(Exception):
    """Base exception for all WordPress API wrapper errors."""
    pass


class AuthenticationError(WordPressError):
    """Raised when WordPress authentication fails."""
    pass


class RateLimitError(WordPressError):
    """Raised when WordPress API rate limits are exceeded."""
    
    def __init__(self, message: str, retry_after: int = None):
        super().__init__(message)
        self.retry_after = retry_after


class MediaUploadError(WordPressError):
    """Raised when media upload operations fail."""
    pass


class PostError(WordPressError):
    """Raised when post operations fail."""
    pass


class CommentError(WordPressError):
    """Raised when comment operations fail."""
    pass


class ConfigurationError(WordPressError):
    """Raised when WordPress configuration is invalid."""
    pass


class NetworkError(WordPressError):
    """Raised when network operations fail."""
    pass


class ValidationError(WordPressError):
    """Raised when data validation fails."""
    pass
