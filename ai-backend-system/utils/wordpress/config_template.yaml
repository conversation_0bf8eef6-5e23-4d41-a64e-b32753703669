# WordPress API Wrapper Configuration Template
# Copy this template to your main config.yaml and customize for your setup

# WordPress API configurations for different regions
wordpress_apis:
  # Australia region example
  au:
    api_url: https://au.yoursite.com/wp-json/wp/v2/
    username: your_username
    password: your_password_or_app_password
    auth_method: basic  # Options: basic, app_password, jwt, oauth
    timeout: 30
    max_retries: 3
    retry_delay: 1.0
    
    # Optional JWT settings (if using JWT auth)
    # jwt_endpoint: https://au.yoursite.com/wp-json/jwt-auth/v1/token
    # jwt_secret_key: your_jwt_secret
    
    # Optional OAuth settings (if using OAuth)
    # oauth_client_id: your_oauth_client_id
    # oauth_client_secret: your_oauth_client_secret
    # oauth_redirect_uri: https://yourapp.com/oauth/callback
  
  # United States region example
  us:
    api_url: https://us.yoursite.com/wp-json/wp/v2/
    username: us_username
    password: us_app_password
    auth_method: app_password  # Recommended for security
    timeout: 30
    max_retries: 3
    retry_delay: 1.0
  
  # Germany region example
  de:
    api_url: https://de.yoursite.com/wp-json/wp/v2/
    username: de_username
    password: de_password
    auth_method: basic
    timeout: 30
    max_retries: 3
    retry_delay: 1.0
  
  # United Kingdom region example
  uk:
    api_url: https://uk.yoursite.com/wp-json/wp/v2/
    username: uk_username
    password: uk_password
    auth_method: app_password
    timeout: 30
    max_retries: 3
    retry_delay: 1.0

# WordPress wrapper configuration
wordpress:
  # Rate limiting settings to prevent API abuse
  rate_limiting:
    requests_per_minute: 60      # Maximum requests per minute
    requests_per_hour: 1000      # Maximum requests per hour (if supported)
    burst_limit: 10              # Maximum burst requests
  
  # Media upload and management settings
  media:
    max_file_size_mb: 50         # Maximum file size in MB
    
    # Supported file formats
    supported_image_formats: [jpg, jpeg, png, gif, webp, svg, bmp, tiff]
    supported_video_formats: [mp4, avi, mov, wmv, flv, webm, mkv, m4v]
    supported_audio_formats: [mp3, wav, ogg, m4a, aac, flac]
    supported_document_formats: [pdf, doc, docx, txt, rtf, odt, xls, xlsx, ppt, pptx]
    
    # Image optimization settings
    image_optimization:
      enabled: true              # Enable automatic image optimization
      max_width: 1920           # Maximum image width in pixels
      max_height: 1080          # Maximum image height in pixels
      quality: 85               # JPEG quality (1-100)
      
    # Video processing settings (if needed)
    video_processing:
      enabled: false            # Enable video processing
      max_duration_seconds: 300 # Maximum video duration
      
  # Post management default settings
  posts:
    default_status: draft                    # Default post status: draft, published, private, pending
    default_comment_status: open            # Default comment status: open, closed
    default_ping_status: closed             # Default ping status: open, closed
    auto_excerpt_length: 150                # Auto-generated excerpt length
    default_categories: []                   # Default categories for new posts
    default_tags: []                        # Default tags for new posts
    
    # Content processing
    auto_format: true                       # Automatically format content
    strip_unsafe_html: true                 # Remove potentially unsafe HTML
    
  # Comment management settings
  comments:
    default_status: hold                    # Default comment status: approved, hold, spam, trash
    moderation_required: true               # Require moderation for new comments
    auto_approve_registered_users: false    # Auto-approve comments from registered users
    max_comment_length: 5000               # Maximum comment length in characters
    
    # Spam detection settings
    spam_detection:
      enabled: true                         # Enable basic spam detection
      max_links: 3                         # Maximum links allowed in comments
      blocked_keywords: [                   # Keywords that trigger spam detection
        'viagra', 'casino', 'lottery', 'winner',
        'congratulations', 'click here', 'free money'
      ]
      
    # Comment threading
    threading:
      enabled: true                         # Enable comment threading
      max_depth: 5                         # Maximum thread depth
      
  # Batch operation settings
  batch:
    max_posts_per_batch: 20                # Maximum posts in batch operations
    max_comments_per_batch: 50             # Maximum comments in batch operations
    max_media_per_batch: 10                # Maximum media files in batch operations
    
    # Batch processing delays
    delay_between_operations: 0.1          # Delay between individual operations (seconds)
    delay_between_batches: 1.0             # Delay between batch operations (seconds)
    
  # Caching settings
  cache:
    enabled: true                          # Enable response caching
    ttl_seconds: 300                       # Cache time-to-live in seconds
    max_cache_size: 1000                   # Maximum number of cached responses
    
  # Logging settings specific to WordPress operations
  logging:
    log_api_requests: true                 # Log all API requests
    log_api_responses: false               # Log API responses (enable for debugging only)
    log_media_uploads: true                # Log media upload operations
    log_authentication: true               # Log authentication attempts
    log_rate_limiting: true                # Log rate limiting events
    log_errors: true                       # Log all errors
    
    # Log levels for different operations
    levels:
      api_requests: INFO
      media_uploads: INFO
      authentication: INFO
      errors: ERROR
      
  # Performance monitoring
  monitoring:
    enabled: true                          # Enable performance monitoring
    metrics_retention_hours: 24            # How long to keep metrics
    alert_thresholds:
      error_rate_percent: 10               # Alert if error rate exceeds this
      avg_response_time_seconds: 5.0       # Alert if avg response time exceeds this
      rate_limit_hit_count: 5              # Alert if rate limits hit this many times
      
  # Security settings
  security:
    verify_ssl: true                       # Verify SSL certificates
    user_agent: "AI-Backend-System/1.0 WordPress-Wrapper"  # Custom user agent
    
    # Request headers
    default_headers:
      Accept: "application/json"
      Content-Type: "application/json"
      
  # Content validation
  validation:
    validate_html: true                    # Validate HTML content
    max_title_length: 200                  # Maximum post title length
    max_content_length: 1000000           # Maximum post content length
    
    # Required fields validation
    required_post_fields: [title, content]
    required_comment_fields: [content]
    
  # Backup and recovery
  backup:
    enabled: false                         # Enable automatic backups before operations
    backup_location: "backups/"            # Local backup directory
    retention_days: 30                     # How long to keep backups
    
  # Development and testing
  development:
    dry_run: false                         # Enable dry run mode (no actual API calls)
    mock_responses: false                  # Use mock responses for testing
    debug_mode: false                      # Enable debug mode
    
    # Test data
    test_post_prefix: "[TEST]"             # Prefix for test posts
    test_category: "Test Content"          # Category for test content
    
# Environment-specific overrides
# These settings can override the above based on environment variables

# Production environment
production:
  wordpress:
    rate_limiting:
      requests_per_minute: 120             # Higher limits for production
    logging:
      log_api_responses: false             # Disable response logging in production
    security:
      verify_ssl: true                     # Always verify SSL in production
      
# Development environment  
development:
  wordpress:
    rate_limiting:
      requests_per_minute: 30              # Lower limits for development
    logging:
      log_api_responses: true              # Enable response logging for debugging
    development:
      debug_mode: true                     # Enable debug mode
      
# Testing environment
testing:
  wordpress:
    development:
      dry_run: true                        # Enable dry run for tests
      mock_responses: true                 # Use mock responses
    logging:
      log_api_requests: false              # Reduce logging noise in tests
