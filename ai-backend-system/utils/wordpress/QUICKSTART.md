# WordPress API Wrapper - Quick Start Guide

Get up and running with the WordPress API wrapper for AutoGen agents in 5 minutes.

## Prerequisites

- Python 3.9+
- WordPress site with REST API enabled
- WordPress user account with appropriate permissions
- AI Backend System environment set up

## Step 1: Install Dependencies

The WordPress wrapper requires these additional packages:

```bash
pip install aiohttp pillow aiofiles pyjwt
```

## Step 2: Configure WordPress API Access

### Option A: Application Passwords (Recommended)

1. Log into your WordPress admin dashboard
2. Go to Users → Your Profile
3. Scroll down to "Application Passwords"
4. Enter a name (e.g., "AI Backend System")
5. Click "Add New Application Password"
6. Copy the generated password (you won't see it again!)

### Option B: Basic Authentication

Use your regular WordPress username and password (less secure).

## Step 3: Update Configuration

Add your WordPress configuration to `ai-backend-system/config/config.yaml`:

```yaml
wordpress_apis:
  au:  # Your region code
    api_url: https://yoursite.com/wp-json/wp/v2/
    username: your_username
    password: your_application_password  # or regular password
    auth_method: app_password  # or 'basic'
    timeout: 30
    max_retries: 3

wordpress:
  rate_limiting:
    requests_per_minute: 60
  media:
    max_file_size_mb: 50
  posts:
    default_status: draft
  comments:
    default_status: hold
```

## Step 4: Test the Connection

Create a test script to verify everything works:

```python
# test_wordpress.py
import asyncio
from utils.wordpress import WordPressClient

async def test_connection():
    async with WordPressClient(region_code='au') as client:
        # Test by getting existing posts
        posts = await client.get_posts(per_page=1)
        print(f"✅ Connected! Found {len(posts)} posts")
        
        # Test creating a post
        post = await client.create_post(
            title="Test Post from AI",
            content="<p>This is a test post created by the WordPress API wrapper.</p>",
            status="draft"
        )
        print(f"✅ Created test post: {post['id']}")

if __name__ == "__main__":
    asyncio.run(test_connection())
```

Run the test:

```bash
cd ai-backend-system
python test_wordpress.py
```

## Step 5: Basic Usage Examples

### Create a Post

```python
from utils.wordpress import WordPressClient

async def create_article():
    async with WordPressClient(region_code='au') as client:
        post = await client.create_post(
            title="My First AI Article",
            content="""
            <h2>Introduction</h2>
            <p>This article was created by an AI agent.</p>
            """,
            categories=["Technology"],
            tags=["ai", "automation"],
            status="published"
        )
        return post
```

### Upload Media

```python
async def upload_image():
    async with WordPressClient(region_code='au') as client:
        media_handler = client.get_media_handler()
        
        media = await media_handler.upload_from_file(
            "path/to/image.jpg",
            title="Featured Image",
            alt_text="Description of the image"
        )
        return media
```

### Manage Comments

```python
async def moderate_comments():
    async with WordPressClient(region_code='au') as client:
        # Get pending comments
        pending = await client.get_comments(status='hold')
        
        # Approve the first one
        if pending:
            await client.moderate_comment(pending[0]['id'], 'approved')
```

## Step 6: Integration with AutoGen Agents

```python
from autogen_agentchat.agents import AssistantAgent
from utils.wordpress import WordPressClient

class BlogPublisherAgent(AssistantAgent):
    def __init__(self):
        super().__init__(
            name="blog_publisher",
            description="Publishes content to WordPress"
        )
    
    async def publish_post(self, title: str, content: str):
        async with WordPressClient(region_code='au') as client:
            post = await client.create_post(
                title=title,
                content=content,
                status='draft'  # Start as draft for review
            )
            return f"Published post {post['id']}: {title}"

# Usage
agent = BlogPublisherAgent()
result = await agent.publish_post(
    "AI-Generated Article", 
    "<p>Content created by AI agent</p>"
)
```

## Common Issues & Solutions

### 1. Authentication Errors

**Problem**: `AuthenticationError: WordPress authentication failed`

**Solutions**:
- Verify username and password/application password
- Check that the WordPress user has sufficient permissions
- Ensure REST API is enabled on your WordPress site

### 2. Connection Timeouts

**Problem**: Requests timing out

**Solutions**:
- Increase `timeout` value in configuration
- Check your WordPress site is accessible
- Verify the `api_url` is correct

### 3. Permission Errors

**Problem**: `403 Forbidden` errors

**Solutions**:
- Ensure WordPress user can create posts/upload media
- Check WordPress user role (should be Editor or Administrator)
- Verify REST API permissions

### 4. Rate Limiting

**Problem**: `429 Too Many Requests`

**Solutions**:
- Reduce `requests_per_minute` in configuration
- Add delays between operations
- Use batch operations for multiple items

## Next Steps

1. **Read the full documentation**: `README.md`
2. **Explore examples**: `examples.py`
3. **Run tests**: `pytest tests/test_wordpress_wrapper.py`
4. **Monitor usage**: Check the monitoring features
5. **Customize configuration**: Use `config_template.yaml`

## Getting Help

- Check the logs in `logs/ai_backend_system.log`
- Enable debug logging for more details
- Review WordPress REST API documentation
- Test with WordPress REST API directly using curl

## Security Best Practices

1. **Use Application Passwords** instead of regular passwords
2. **Use HTTPS** for all WordPress sites
3. **Limit user permissions** to only what's needed
4. **Regularly rotate** application passwords
5. **Monitor API usage** for unusual activity
6. **Keep WordPress updated** for security patches

## Performance Tips

1. **Use batch operations** for multiple items
2. **Enable caching** in configuration
3. **Optimize images** before upload
4. **Monitor rate limits** and adjust accordingly
5. **Use appropriate timeouts** for your network

---

You're now ready to use the WordPress API wrapper with your AutoGen agents! 🚀
