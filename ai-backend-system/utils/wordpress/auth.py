# ai-backend-system/utils/wordpress/auth.py
"""
WordPress Authentication module for AutoGen agents.

This module provides various authentication methods for WordPress REST API,
including Basic Auth, Application Passwords, JWT, and OAuth.

Features:
- Multiple authentication methods
- Token management and refresh
- Secure credential handling
- Integration with configuration system
"""

import asyncio
import base64
import json
import logging
import time
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, Optional, Any
import aiohttp
import jwt

from config import get_config
from utils.wordpress.exceptions import AuthenticationError, ConfigurationError


class AuthenticationProvider(ABC):
    """Abstract base class for WordPress authentication providers."""
    
    @abstractmethod
    async def get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for API requests."""
        pass
    
    @abstractmethod
    async def refresh_credentials(self) -> bool:
        """Refresh authentication credentials if needed."""
        pass
    
    @abstractmethod
    def is_valid(self) -> bool:
        """Check if current credentials are valid."""
        pass


class BasicAuthProvider(AuthenticationProvider):
    """Basic Authentication provider for WordPress."""
    
    def __init__(self, username: str, password: str):
        """
        Initialize Basic Auth provider.
        
        Args:
            username (str): WordPress username
            password (str): WordPress password
        """
        self.username = username
        self.password = password
        self.logger = logging.getLogger(__name__)
    
    async def get_auth_headers(self) -> Dict[str, str]:
        """Get Basic Auth headers."""
        credentials = base64.b64encode(f"{self.username}:{self.password}".encode()).decode()
        return {'Authorization': f'Basic {credentials}'}
    
    async def refresh_credentials(self) -> bool:
        """Basic auth doesn't need refresh."""
        return True
    
    def is_valid(self) -> bool:
        """Check if credentials are provided."""
        return bool(self.username and self.password)


class ApplicationPasswordProvider(AuthenticationProvider):
    """Application Password provider for WordPress."""
    
    def __init__(self, username: str, app_password: str):
        """
        Initialize Application Password provider.
        
        Args:
            username (str): WordPress username
            app_password (str): WordPress application password
        """
        self.username = username
        self.app_password = app_password
        self.logger = logging.getLogger(__name__)
    
    async def get_auth_headers(self) -> Dict[str, str]:
        """Get Application Password auth headers."""
        credentials = base64.b64encode(f"{self.username}:{self.app_password}".encode()).decode()
        return {'Authorization': f'Basic {credentials}'}
    
    async def refresh_credentials(self) -> bool:
        """Application passwords don't need refresh."""
        return True
    
    def is_valid(self) -> bool:
        """Check if credentials are provided."""
        return bool(self.username and self.app_password)


class JWTAuthProvider(AuthenticationProvider):
    """JWT Authentication provider for WordPress."""
    
    def __init__(self, username: str, password: str, jwt_endpoint: str, secret_key: str = None):
        """
        Initialize JWT Auth provider.
        
        Args:
            username (str): WordPress username
            password (str): WordPress password
            jwt_endpoint (str): JWT authentication endpoint
            secret_key (str): JWT secret key (optional)
        """
        self.username = username
        self.password = password
        self.jwt_endpoint = jwt_endpoint
        self.secret_key = secret_key
        self.token = None
        self.token_expires = None
        self.logger = logging.getLogger(__name__)
    
    async def get_auth_headers(self) -> Dict[str, str]:
        """Get JWT auth headers."""
        if not self.is_valid():
            await self.refresh_credentials()
        
        if not self.token:
            raise AuthenticationError("No valid JWT token available")
        
        return {'Authorization': f'Bearer {self.token}'}
    
    async def refresh_credentials(self) -> bool:
        """Refresh JWT token."""
        try:
            async with aiohttp.ClientSession() as session:
                auth_data = {
                    'username': self.username,
                    'password': self.password
                }
                
                async with session.post(self.jwt_endpoint, json=auth_data) as response:
                    if response.status != 200:
                        raise AuthenticationError(f"JWT authentication failed: {response.status}")
                    
                    data = await response.json()
                    
                    if 'token' not in data:
                        raise AuthenticationError("No token in JWT response")
                    
                    self.token = data['token']
                    
                    # Decode token to get expiration
                    try:
                        decoded = jwt.decode(self.token, options={"verify_signature": False})
                        if 'exp' in decoded:
                            self.token_expires = datetime.fromtimestamp(decoded['exp'])
                        else:
                            # Default to 1 hour if no expiration
                            self.token_expires = datetime.now() + timedelta(hours=1)
                    except Exception as e:
                        self.logger.warning(f"Failed to decode JWT token: {str(e)}")
                        self.token_expires = datetime.now() + timedelta(hours=1)
                    
                    self.logger.info("JWT token refreshed successfully")
                    return True
                    
        except Exception as e:
            self.logger.error(f"Failed to refresh JWT token: {str(e)}")
            raise AuthenticationError(f"JWT token refresh failed: {str(e)}")
    
    def is_valid(self) -> bool:
        """Check if JWT token is valid and not expired."""
        if not self.token or not self.token_expires:
            return False
        
        # Check if token expires within next 5 minutes
        return datetime.now() + timedelta(minutes=5) < self.token_expires


class OAuthProvider(AuthenticationProvider):
    """OAuth provider for WordPress (placeholder for future implementation)."""
    
    def __init__(self, client_id: str, client_secret: str, redirect_uri: str):
        """
        Initialize OAuth provider.
        
        Args:
            client_id (str): OAuth client ID
            client_secret (str): OAuth client secret
            redirect_uri (str): OAuth redirect URI
        """
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri
        self.access_token = None
        self.refresh_token = None
        self.token_expires = None
        self.logger = logging.getLogger(__name__)
    
    async def get_auth_headers(self) -> Dict[str, str]:
        """Get OAuth auth headers."""
        raise AuthenticationError("OAuth authentication not yet implemented")
    
    async def refresh_credentials(self) -> bool:
        """Refresh OAuth token."""
        raise AuthenticationError("OAuth authentication not yet implemented")
    
    def is_valid(self) -> bool:
        """Check if OAuth token is valid."""
        return False  # Not implemented yet


class AuthenticationManager:
    """Manages authentication providers for WordPress clients."""
    
    def __init__(self, api_config: Dict[str, Any]):
        """
        Initialize authentication manager.
        
        Args:
            api_config (Dict[str, Any]): API configuration
        """
        self.api_config = api_config
        self.provider = None
        self.logger = logging.getLogger(__name__)
        
        self._initialize_provider()
    
    def _initialize_provider(self):
        """Initialize the appropriate authentication provider."""
        auth_method = self.api_config.get('auth_method', 'basic')
        username = self.api_config.get('username')
        password = self.api_config.get('password')
        
        if not username or not password:
            raise ConfigurationError("Username and password required for authentication")
        
        if auth_method == 'basic':
            self.provider = BasicAuthProvider(username, password)
        elif auth_method == 'app_password':
            self.provider = ApplicationPasswordProvider(username, password)
        elif auth_method == 'jwt':
            jwt_endpoint = self.api_config.get('jwt_endpoint')
            if not jwt_endpoint:
                raise ConfigurationError("JWT endpoint required for JWT authentication")
            secret_key = self.api_config.get('jwt_secret_key')
            self.provider = JWTAuthProvider(username, password, jwt_endpoint, secret_key)
        elif auth_method == 'oauth':
            client_id = self.api_config.get('oauth_client_id')
            client_secret = self.api_config.get('oauth_client_secret')
            redirect_uri = self.api_config.get('oauth_redirect_uri')
            
            if not all([client_id, client_secret, redirect_uri]):
                raise ConfigurationError("OAuth credentials required for OAuth authentication")
            
            self.provider = OAuthProvider(client_id, client_secret, redirect_uri)
        else:
            raise ConfigurationError(f"Unsupported authentication method: {auth_method}")
        
        self.logger.info(f"Initialized {auth_method} authentication provider")
    
    async def get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers from the current provider."""
        if not self.provider:
            raise AuthenticationError("No authentication provider initialized")
        
        return await self.provider.get_auth_headers()
    
    async def refresh_credentials(self) -> bool:
        """Refresh authentication credentials."""
        if not self.provider:
            raise AuthenticationError("No authentication provider initialized")
        
        return await self.provider.refresh_credentials()
    
    def is_valid(self) -> bool:
        """Check if current authentication is valid."""
        if not self.provider:
            return False
        
        return self.provider.is_valid()
