# ai-backend-system/utils/wordpress/examples.py
"""
WordPress API Wrapper Usage Examples

This module provides practical examples of how to use the WordPress API wrapper
with AutoGen agents for various content management scenarios.

Examples include:
- Basic post creation and management
- Media upload and optimization
- Comment moderation workflows
- Batch operations
- Integration with AutoGen agents
- Error handling patterns
"""

import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

from utils.wordpress import WordPressClient, MediaHandler
from utils.wordpress.exceptions import (
    WordPressError,
    AuthenticationError,
    RateLimitError,
    PostError,
    CommentError,
    MediaUploadError
)


async def basic_post_creation_example():
    """Example: Create a basic WordPress post."""
    print("=== Basic Post Creation Example ===")
    
    async with WordPressClient(region_code='au') as client:
        try:
            # Create a simple post
            post = await client.create_post(
                title="Welcome to AI-Generated Content",
                content="""
                <h2>Introduction</h2>
                <p>This post was created by an AutoGen agent using the WordPress API wrapper.</p>
                
                <h3>Features</h3>
                <ul>
                    <li>Automated content creation</li>
                    <li>Multi-region support</li>
                    <li>Comprehensive error handling</li>
                </ul>
                
                <p>Thank you for reading!</p>
                """,
                status="draft",  # Start as draft
                categories=["Technology", "AI"],
                tags=["automation", "wordpress", "ai"],
                excerpt="An introduction to AI-generated WordPress content."
            )
            
            print(f"✅ Created post: {post['title']['rendered']}")
            print(f"   Post ID: {post['id']}")
            print(f"   Status: {post['status']}")
            print(f"   URL: {post['link']}")
            
            return post
            
        except PostError as e:
            print(f"❌ Failed to create post: {e}")
            return None


async def media_upload_example():
    """Example: Upload and manage media files."""
    print("\n=== Media Upload Example ===")
    
    async with WordPressClient(region_code='au') as client:
        media_handler = client.get_media_handler()
        
        try:
            # Example: Upload an image (you would replace with actual file)
            # For demo purposes, we'll create fake image data
            fake_image_data = b"fake_image_content_here"
            
            media = await media_handler.upload_media(
                file_data=fake_image_data,
                filename="ai-generated-image.jpg",
                title="AI Generated Featured Image",
                alt_text="An image created by AI for demonstration",
                caption="This image showcases AI capabilities"
            )
            
            print(f"✅ Uploaded media: {media['title']['rendered']}")
            print(f"   Media ID: {media['id']}")
            print(f"   URL: {media['source_url']}")
            
            return media
            
        except MediaUploadError as e:
            print(f"❌ Failed to upload media: {e}")
            return None


async def comment_management_example():
    """Example: Create and moderate comments."""
    print("\n=== Comment Management Example ===")
    
    async with WordPressClient(region_code='au') as client:
        try:
            # First, get a post to comment on
            posts = await client.get_posts(per_page=1, status='published')
            if not posts:
                print("❌ No published posts found to comment on")
                return
            
            post_id = posts[0]['id']
            
            # Create a comment
            comment = await client.create_comment(
                post_id=post_id,
                content="This is an excellent article! Thanks for sharing these insights.",
                author_name="AI Assistant",
                author_email="<EMAIL>",
                status="hold"  # Start in moderation
            )
            
            print(f"✅ Created comment: {comment['id']}")
            print(f"   Post ID: {comment['post']}")
            print(f"   Status: {comment['status']}")
            
            # Moderate the comment (approve it)
            moderated = await client.moderate_comment(comment['id'], action='approved')
            print(f"✅ Approved comment: {moderated['id']}")
            
            # Get all comments for the post
            all_comments = await client.get_comments(post_id=post_id)
            print(f"📝 Total comments on post {post_id}: {len(all_comments)}")
            
            return comment
            
        except CommentError as e:
            print(f"❌ Comment operation failed: {e}")
            return None


async def batch_operations_example():
    """Example: Perform batch operations efficiently."""
    print("\n=== Batch Operations Example ===")
    
    async with WordPressClient(region_code='au') as client:
        try:
            # Create multiple posts in batch
            posts_data = [
                {
                    'title': f'AI Article {i}',
                    'content': f'<p>This is AI-generated content for article {i}.</p>',
                    'categories': ['AI', 'Technology'],
                    'tags': [f'article-{i}', 'batch-created'],
                    'status': 'draft'
                }
                for i in range(1, 4)
            ]
            
            batch_results = await client.create_posts_batch(posts_data)
            
            successful_posts = [r for r in batch_results if 'error' not in r]
            failed_posts = [r for r in batch_results if 'error' in r]
            
            print(f"✅ Batch created {len(successful_posts)} posts successfully")
            if failed_posts:
                print(f"❌ {len(failed_posts)} posts failed to create")
            
            # Get comment IDs for batch moderation (example)
            comments = await client.get_comments(status='hold', per_page=5)
            if comments:
                comment_ids = [c['id'] for c in comments]
                moderation_results = await client.moderate_comments_batch(
                    comment_ids, 
                    action='approved'
                )
                print(f"✅ Batch moderated {len(moderation_results)} comments")
            
            return batch_results
            
        except (PostError, CommentError) as e:
            print(f"❌ Batch operation failed: {e}")
            return None


async def error_handling_example():
    """Example: Comprehensive error handling."""
    print("\n=== Error Handling Example ===")
    
    try:
        # Try to create client with invalid region
        async with WordPressClient(region_code='invalid_region') as client:
            await client.create_post(title="Test", content="Test")
            
    except AuthenticationError:
        print("❌ Authentication failed - check credentials")
    except RateLimitError as e:
        print(f"❌ Rate limited - retry after {e.retry_after} seconds")
    except PostError as e:
        print(f"❌ Post operation failed: {e}")
    except WordPressError as e:
        print(f"❌ General WordPress error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


async def monitoring_example():
    """Example: Monitor API usage and performance."""
    print("\n=== Monitoring Example ===")
    
    from utils.wordpress.monitoring import get_monitor
    
    # Get monitor for a specific region
    monitor = get_monitor('au')
    
    # Perform some operations to generate metrics
    async with WordPressClient(region_code='au') as client:
        try:
            # Get some posts to generate API calls
            posts = await client.get_posts(per_page=5)
            print(f"📊 Retrieved {len(posts)} posts")
            
            # Get metrics summary
            metrics = monitor.get_metrics_summary()
            
            print(f"📈 API Metrics for region 'au':")
            print(f"   Total requests: {metrics['requests']['total']}")
            print(f"   Success rate: {metrics['requests']['success_rate']:.1f}%")
            print(f"   Average response time: {metrics['performance']['avg_response_time']:.3f}s")
            print(f"   Posts created: {metrics['operations']['posts_created']}")
            print(f"   Comments created: {metrics['operations']['comments_created']}")
            print(f"   Media uploaded: {metrics['operations']['media_uploaded']}")
            
            if metrics['errors']['rate_limit_hits'] > 0:
                print(f"⚠️  Rate limit hits: {metrics['errors']['rate_limit_hits']}")
            
            if metrics['errors']['auth_failures'] > 0:
                print(f"⚠️  Auth failures: {metrics['errors']['auth_failures']}")
                
        except Exception as e:
            print(f"❌ Monitoring example failed: {e}")


class ContentPublisherAgent:
    """Example AutoGen agent for publishing content to WordPress."""
    
    def __init__(self, region_code: str = 'au'):
        self.region_code = region_code
        self.logger = logging.getLogger(f"{__name__}.ContentPublisher.{region_code}")
    
    async def publish_article(
        self, 
        title: str, 
        content: str, 
        categories: List[str] = None,
        tags: List[str] = None,
        featured_image_path: str = None
    ) -> Dict[str, Any]:
        """
        Publish a complete article with optional featured image.
        
        Args:
            title: Article title
            content: Article content (HTML)
            categories: List of category names
            tags: List of tag names
            featured_image_path: Path to featured image file
            
        Returns:
            Dict containing post information and status
        """
        async with WordPressClient(region_code=self.region_code) as client:
            try:
                # Upload featured image if provided
                featured_media_id = None
                if featured_image_path and Path(featured_image_path).exists():
                    media_handler = client.get_media_handler()
                    media = await media_handler.upload_from_file(
                        featured_image_path,
                        title=f"Featured image for: {title}",
                        alt_text=f"Featured image for article: {title}"
                    )
                    featured_media_id = media['id']
                    self.logger.info(f"Uploaded featured image: {media['id']}")
                
                # Create the post
                post = await client.create_post(
                    title=title,
                    content=content,
                    categories=categories or [],
                    tags=tags or [],
                    featured_media=featured_media_id,
                    status='draft'  # Start as draft for review
                )
                
                self.logger.info(f"Published article: {post['id']} - {title}")
                
                return {
                    'success': True,
                    'post_id': post['id'],
                    'post_url': post['link'],
                    'status': post['status'],
                    'featured_media_id': featured_media_id
                }
                
            except Exception as e:
                self.logger.error(f"Failed to publish article '{title}': {e}")
                return {
                    'success': False,
                    'error': str(e)
                }
    
    async def update_post_status(self, post_id: int, status: str) -> bool:
        """Update the status of a published post."""
        async with WordPressClient(region_code=self.region_code) as client:
            try:
                await client.update_post(post_id, status=status)
                self.logger.info(f"Updated post {post_id} status to: {status}")
                return True
            except Exception as e:
                self.logger.error(f"Failed to update post {post_id} status: {e}")
                return False


class CommentModeratorAgent:
    """Example AutoGen agent for moderating WordPress comments."""
    
    def __init__(self, region_code: str = 'au'):
        self.region_code = region_code
        self.logger = logging.getLogger(f"{__name__}.CommentModerator.{region_code}")
        
        # Simple spam detection keywords (in real implementation, use ML)
        self.spam_keywords = [
            'viagra', 'casino', 'lottery', 'winner', 'congratulations',
            'click here', 'free money', 'make money fast'
        ]
    
    async def moderate_pending_comments(self) -> Dict[str, int]:
        """
        Review and moderate all pending comments.
        
        Returns:
            Dict with counts of approved, rejected, and spam comments
        """
        async with WordPressClient(region_code=self.region_code) as client:
            try:
                # Get all pending comments
                pending_comments = await client.get_comments(
                    status='hold',
                    per_page=50  # Process in batches
                )
                
                approved_count = 0
                spam_count = 0
                rejected_count = 0
                
                for comment in pending_comments:
                    content = comment['content']['rendered'].lower()
                    
                    if self._is_spam(content):
                        await client.moderate_comment(comment['id'], 'spam')
                        spam_count += 1
                    elif self._is_appropriate(content):
                        await client.moderate_comment(comment['id'], 'approved')
                        approved_count += 1
                    else:
                        # Keep in moderation or reject
                        rejected_count += 1
                
                self.logger.info(
                    f"Moderated {len(pending_comments)} comments: "
                    f"{approved_count} approved, {spam_count} spam, {rejected_count} rejected"
                )
                
                return {
                    'approved': approved_count,
                    'spam': spam_count,
                    'rejected': rejected_count,
                    'total_processed': len(pending_comments)
                }
                
            except Exception as e:
                self.logger.error(f"Comment moderation failed: {e}")
                return {'error': str(e)}
    
    def _is_spam(self, content: str) -> bool:
        """Simple spam detection based on keywords."""
        return any(keyword in content for keyword in self.spam_keywords)
    
    def _is_appropriate(self, content: str) -> bool:
        """Check if comment content is appropriate."""
        # Basic checks
        if len(content.strip()) < 5:  # Too short
            return False
        
        if content.count('http') > 2:  # Too many links
            return False
        
        # Add more sophisticated checks as needed
        return True


async def agent_integration_example():
    """Example: Using WordPress wrapper with AutoGen agents."""
    print("\n=== AutoGen Agent Integration Example ===")
    
    # Initialize agents
    publisher = ContentPublisherAgent(region_code='au')
    moderator = CommentModeratorAgent(region_code='au')
    
    # Publish an article
    article_result = await publisher.publish_article(
        title="The Future of AI in Content Creation",
        content="""
        <h2>Introduction</h2>
        <p>Artificial Intelligence is revolutionizing how we create and manage content...</p>
        
        <h3>Key Benefits</h3>
        <ul>
            <li>Automated content generation</li>
            <li>Intelligent content optimization</li>
            <li>Real-time content moderation</li>
        </ul>
        
        <h3>Conclusion</h3>
        <p>The integration of AI with content management systems opens new possibilities...</p>
        """,
        categories=["AI", "Technology", "Content"],
        tags=["artificial-intelligence", "automation", "cms"]
    )
    
    if article_result['success']:
        print(f"✅ Agent published article: {article_result['post_id']}")
        
        # Update to published status
        await publisher.update_post_status(article_result['post_id'], 'published')
        print(f"✅ Article status updated to published")
    else:
        print(f"❌ Agent failed to publish article: {article_result['error']}")
    
    # Moderate comments
    moderation_result = await moderator.moderate_pending_comments()
    if 'error' not in moderation_result:
        print(f"✅ Agent moderated comments:")
        print(f"   Approved: {moderation_result['approved']}")
        print(f"   Marked as spam: {moderation_result['spam']}")
        print(f"   Rejected: {moderation_result['rejected']}")
    else:
        print(f"❌ Comment moderation failed: {moderation_result['error']}")


async def main():
    """Run all examples."""
    print("🚀 WordPress API Wrapper Examples\n")
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        # Run examples
        await basic_post_creation_example()
        await media_upload_example()
        await comment_management_example()
        await batch_operations_example()
        await error_handling_example()
        await monitoring_example()
        await agent_integration_example()
        
        print("\n✅ All examples completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Examples failed with error: {e}")


if __name__ == "__main__":
    # Run examples
    asyncio.run(main())
