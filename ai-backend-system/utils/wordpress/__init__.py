# ai-backend-system/utils/wordpress/__init__.py
"""
WordPress utilities package initialization module.

This module provides a comprehensive WordPress API wrapper for AutoGen agents
to interact with WordPress through a clean, well-structured interface.

Core components:
- WordPressClient: Main client for WordPress API operations
- MediaHandler: Handles media upload and management
- Authentication: Manages different WordPress authentication methods
- Rate limiting and error handling utilities
"""

from .wordpress_client import WordPressClient, create_wordpress_client
from .media_handler import MediaHandler
from .exceptions import (
    WordPressError,
    AuthenticationError,
    RateLimitError,
    MediaUploadError,
    PostError,
    CommentError
)

__all__ = [
    'WordPressClient',
    'MediaHandler',
    'create_wordpress_client',
    'WordPressError',
    'AuthenticationError', 
    'RateLimitError',
    'MediaUploadError',
    'PostError',
    'CommentError'
]
