# ai-backend-system/tests/test_utils.py
"""
Util 模块 (utils) 的单元测试模块.

该模块包含了针对 utils 目录下的所有 Util 类 (ScrapyUtil, DataProcessingUtil, WordPressUtil, DBConnector) 的单元测试用例.
使用 pytest 框架编写单元测试，验证 Util 类的核心功能和工具函数是否正确.

测试用例包括：
- Util 类的初始化测试 (例如测试 ScrapyUtil 实例是否成功创建, WordPressUtil 是否成功连接 WordPress API)
- Util 类的核心方法的功能测试 (例如测试 ScrapyUtil 的 run_spider 方法, DataProcessingUtil 的 clean_deal_data 方法, WordPressUtil 的 publish_deal 方法, DBConnector 的 insert_data 方法等)
- 模拟 Util 类依赖的外部服务或库 (例如使用 mock 对象或 unittest.mock 库模拟 Scrapy 爬虫, WordPress API, Supabase 数据库)
- 验证 Util 方法的输入输出、异常处理和 side effects (例如是否正确发送 API 请求, 是否正确读写数据库, 是否抛出预期的异常)

测试套件可以使用 pytest 命令运行，例如在项目根目录下执行 `pytest tests/test_utils.py` 命令.
"""
import pytest
from unittest.mock import MagicMock, patch # For mocking dependencies and patching

# Import Util classes to be tested
from utils.scrapy_util import ScrapyUtil, create_scrapy_util
from utils.data_processing_util import DataProcessingUtil, create_data_processing_util
from utils.wordpress_util import WordPressUtil, create_wordpress_util
from utils.db_connector import DBConnector, create_db_connector

# --- ScrapyUtil tests ---
def test_scrapy_util_initialization():
    """
    测试 ScrapyUtil 实例的初始化.
    验证 ScrapyUtil 实例是否成功创建.
    """
    util = create_scrapy_util()
    assert isinstance(util, ScrapyUtil)

def test_scrapy_util_run_spider():
    """
    测试 ScrapyUtil 的 run_spider 方法.
    验证该方法是否能正确运行 Scrapy 爬虫，并返回抓取的数据.
    需要 Mock Scrapy 框架和爬虫 Spider 的行为，模拟爬虫运行和数据抓取过程.
    """
    util = create_scrapy_util()
    spider_name = "test_spider" # (示例爬虫名称)
    # (Mock Scrapy 框架和爬虫 Spider 的行为)
    # scraped_data = util.run_spider(spider_name)
    # (验证 scraped_data 是否为列表类型，内容是否符合预期)
    pass

def test_scrapy_util_get_spider_list():
    """
    测试 ScrapyUtil 的 get_spider_list 方法.
    验证该方法是否能正确获取可用的爬虫列表.
    需要 Mock Scrapy 框架的组件，模拟获取爬虫列表的过程.
    """
    util = create_scrapy_util()
    # (Mock Scrapy 框架的组件，例如 CrawlerProcess, SpiderLoader)
    # spider_list = util.get_spider_list()
    # (验证 spider_list 是否为列表类型，内容是否包含预期的爬虫名称)
    pass


# --- DataProcessingUtil tests ---
def test_data_processing_util_initialization():
    """
    测试 DataProcessingUtil 实例的初始化.
    验证 DataProcessingUtil 实例是否成功创建.
    """
    util = create_data_processing_util()
    assert isinstance(util, DataProcessingUtil)

def test_data_processing_util_clean_deal_data():
    """
    测试 DataProcessingUtil 的 clean_deal_data 方法.
    验证该方法是否能正确清洗 Deal 数据，去除噪音数据.
    """
    util = create_data_processing_util()
    raw_deal_data = {} # (示例原始 Deal 数据，包含 HTML 标签、特殊字符等)
    # cleaned_data = util.clean_deal_data(raw_deal_data)
    # (验证 cleaned_data 是否已去除 HTML 标签、特殊字符，数据格式是否符合预期)
    pass

def test_data_processing_util_transform_deal_data():
    """
    测试 DataProcessingUtil 的 transform_deal_data 方法.
    验证该方法是否能正确转换 Deal 数据格式.
    """
    util = create_data_processing_util()
    deal_data = {} # (示例 Deal 数据，字典格式)
    target_format = "json" # (目标格式)
    # transformed_data = util.transform_deal_data(deal_data, target_format)
    # (验证 transformed_data 是否为 JSON 字符串类型，内容是否符合预期)
    pass

# --- WordPressUtil tests ---
def test_wordpress_util_initialization():
    """
    测试 WordPressUtil 实例的初始化.
    验证 WordPressUtil 实例是否成功创建.
    """
    wordpress_api_url = "http://test-wordpress-site.com/wp-json/wp/v2/" # (示例 WordPress API URL)
    util = create_wordpress_util(wordpress_api_url)
    assert isinstance(util, WordPressUtil)
    assert util.api_url == wordpress_api_url

@patch('utils.wordpress_util.requests.post') # 使用 patch 装饰器 Mock requests.post
def test_wordpress_util_publish_deal(mock_post):
    """
    测试 WordPressUtil 的 publish_deal 方法.
    验证该方法是否能正确调用 WordPress REST API 发布 Deal 信息.
    使用 mock_post 对象 Mock requests.post 方法，模拟 API 请求和响应.
    """
    wordpress_api_url = "http://test-wordpress-site.com/wp-json/wp/v2/"
    util = create_wordpress_util(wordpress_api_url, "test_user", "test_password")
    deal_data = {} # (示例 Deal 数据)
    mock_response = MagicMock() # 创建 MagicMock 对象模拟 requests.Response
    mock_response.status_code = 201 # 模拟 API 成功响应状态码 201 (Created)
    mock_response.json.return_value = {"id": 123, "link": "http://test-wordpress-site.com/deals/test-deal"} # 模拟 API 响应 JSON 数据
    mock_post.return_value = mock_response # 设置 mock_post 的返回值

    # api_response = util.publish_deal(deal_data) # 调用 publish_deal 方法
    # assert api_response["id"] == 123 # 验证 API 响应是否包含预期的 ID
    # mock_post.assert_called_once() # 验证 requests.post 方法是否被调用一次


# --- DBConnector tests ---
def test_db_connector_initialization():
    """
    测试 DBConnector 实例的初始化.
    验证 DBConnector 实例是否成功创建.
    """
    supabase_url = "https://test-project-id.supabase.co" # (示例 Supabase URL)
    supabase_key = "test_supabase_key" # (示例 Supabase API 密钥)
    util = create_db_connector(supabase_url, supabase_key)
    assert isinstance(util, DBConnector)
    assert util.supabase_url == supabase_url
    assert util.supabase_key == supabase_key

@patch('utils.db_connector.create_client') # 使用 patch 装饰器 Mock create_client
def test_db_connector_insert_data(mock_create_client):
    """
    测试 DBConnector 的 insert_data 方法.
    验证该方法是否能正确调用 Supabase 客户端 API 插入数据.
    使用 mock_create_client 对象 Mock create_client 函数，模拟 Supabase 客户端创建和 API 调用.
    """
    supabase_url = "https://test-project-id.supabase.co"
    supabase_key = "test_supabase_key"
    util = create_db_connector(supabase_url, supabase_key)
    table_name = "test_table" # (示例表名)
    data_to_insert = {} # (示例要插入的数据)
    mock_supabase_client = MagicMock() # 创建 MagicMock 对象模拟 Supabase 客户端实例
    mock_insert_method = MagicMock() # 创建 MagicMock 对象模拟 Supabase 客户端的 table().insert() 方法
    mock_insert_method.execute.return_value = MagicMock(data=[{"id": 1}]) # 模拟 insert().execute() 返回成功响应
    mock_supabase_client.table.return_value.insert.return_value = mock_insert_method # 设置 mock_supabase_client 的 table().insert() 方法返回值
    mock_create_client.return_value = mock_supabase_client # 设置 mock_create_client 的返回值

    # api_response = util.insert_data(table_name, data_to_insert) # 调用 insert_data 方法
    # assert api_response["data"][0]["id"] == 1 # 验证 API 响应是否包含预期的 ID
    # mock_create_client.assert_called_once() # 验证 create_client 函数是否被调用一次
    # mock_supabase_client.table.assert_called_once_with(table_name) # 验证 table() 方法是否被调用，并传入正确的表名
    # mock_insert_method.execute.assert_called_once() # 验证 insert().execute() 方法是否被调用


# 可以根据需要添加更多 Util 类的单元测试用例，覆盖 Util 类的所有核心功能和工具函数.