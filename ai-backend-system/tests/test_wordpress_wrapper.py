# ai-backend-system/tests/test_wordpress_wrapper.py
"""
WordPress API wrapper unit tests.

This module contains comprehensive unit tests for the WordPress API wrapper,
including tests for all major functionality with multi-user isolation and
mocked external API calls.

Test coverage includes:
- WordPress client initialization and configuration
- Post management operations (CRUD)
- Comment management and moderation
- Media upload and management
- Authentication methods
- Rate limiting and error handling
- Batch operations
"""

import asyncio
import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch, mock_open
from datetime import datetime, timedelta
import aiohttp
from pathlib import Path

# Import WordPress wrapper components
from utils.wordpress import (
    WordPressClient,
    MediaHandler,
    create_wordpress_client,
    WordPressError,
    AuthenticationError,
    RateLimitError,
    MediaUploadError,
    PostError,
    CommentError
)
from utils.wordpress.auth import (
    BasicAuthProvider,
    ApplicationPasswordProvider,
    JWTAuthProvider,
    AuthenticationManager
)


class TestWordPressClient:
    """Test cases for WordPressClient class."""
    
    @pytest.fixture
    def mock_config(self):
        """Mock configuration for testing."""
        return {
            'wordpress_apis': {
                'test': {
                    'api_url': 'https://test.example.com/wp-json/wp/v2/',
                    'username': 'test_user',
                    'password': 'test_pass',
                    'auth_method': 'basic',
                    'timeout': 30,
                    'max_retries': 3,
                    'retry_delay': 1.0
                }
            },
            'wordpress': {
                'rate_limiting': {
                    'requests_per_minute': 60,
                    'burst_limit': 10
                },
                'posts': {
                    'default_status': 'draft'
                },
                'comments': {
                    'default_status': 'hold'
                },
                'batch': {
                    'max_posts_per_batch': 20,
                    'max_comments_per_batch': 50
                }
            }
        }
    
    @pytest.fixture
    def client(self, mock_config):
        """Create WordPress client for testing."""
        with patch('utils.wordpress.wordpress_client.get_config', return_value=mock_config):
            return WordPressClient(region_code='test')
    
    @pytest.mark.asyncio
    async def test_client_initialization(self, mock_config):
        """Test WordPress client initialization."""
        with patch('utils.wordpress.wordpress_client.get_config', return_value=mock_config):
            client = WordPressClient(region_code='test')
            
            assert client.region_code == 'test'
            assert client.api_config['api_url'] == 'https://test.example.com/wp-json/wp/v2/'
            assert client.api_config['username'] == 'test_user'
    
    @pytest.mark.asyncio
    async def test_client_initialization_no_region(self, mock_config):
        """Test client initialization without region code."""
        with patch('utils.wordpress.wordpress_client.get_config', return_value=mock_config):
            client = WordPressClient()
            
            # Should use first available region
            assert client.region_code == 'test'
    
    @pytest.mark.asyncio
    async def test_client_context_manager(self, client):
        """Test client as async context manager."""
        with patch.object(client, '_initialize_session') as mock_init:
            with patch.object(client, '_close_session') as mock_close:
                async with client:
                    pass
                
                mock_init.assert_called_once()
                mock_close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_make_request_success(self, client):
        """Test successful API request."""
        mock_response_data = {'id': 1, 'title': 'Test Post'}
        
        with patch('aiohttp.ClientSession.request') as mock_request:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.text.return_value = json.dumps(mock_response_data)
            mock_request.return_value.__aenter__.return_value = mock_response
            
            client.session = AsyncMock()
            client.auth_manager = AsyncMock()
            client.auth_manager.get_auth_headers.return_value = {'Authorization': 'Basic dGVzdA=='}
            
            result = await client._make_request('GET', 'posts/1')
            
            assert result == mock_response_data
    
    @pytest.mark.asyncio
    async def test_make_request_authentication_error(self, client):
        """Test API request with authentication error."""
        with patch('aiohttp.ClientSession.request') as mock_request:
            mock_response = AsyncMock()
            mock_response.status = 401
            mock_response.text.return_value = '{"message": "Unauthorized"}'
            mock_request.return_value.__aenter__.return_value = mock_response
            
            client.session = AsyncMock()
            client.auth_manager = AsyncMock()
            client.auth_manager.get_auth_headers.return_value = {'Authorization': 'Basic dGVzdA=='}
            
            with pytest.raises(AuthenticationError):
                await client._make_request('GET', 'posts/1')
    
    @pytest.mark.asyncio
    async def test_make_request_rate_limit(self, client):
        """Test API request with rate limiting."""
        with patch('aiohttp.ClientSession.request') as mock_request:
            mock_response = AsyncMock()
            mock_response.status = 429
            mock_response.headers = {'Retry-After': '60'}
            mock_response.text.return_value = '{"message": "Rate limit exceeded"}'
            mock_request.return_value.__aenter__.return_value = mock_response
            
            client.session = AsyncMock()
            client.auth_manager = AsyncMock()
            client.auth_manager.get_auth_headers.return_value = {'Authorization': 'Basic dGVzdA=='}
            
            with pytest.raises(RateLimitError) as exc_info:
                await client._make_request('GET', 'posts/1')
            
            assert exc_info.value.retry_after == 60


class TestPostManagement:
    """Test cases for post management functionality."""
    
    @pytest.fixture
    def client_with_session(self, client):
        """Client with mocked session."""
        client.session = AsyncMock()
        client.auth_manager = AsyncMock()
        client.auth_manager.get_auth_headers.return_value = {'Authorization': 'Basic dGVzdA=='}
        return client
    
    @pytest.mark.asyncio
    async def test_create_post(self, client_with_session):
        """Test post creation."""
        mock_post_data = {
            'id': 1,
            'title': {'rendered': 'Test Post'},
            'content': {'rendered': 'Test content'},
            'status': 'draft'
        }
        
        with patch.object(client_with_session, '_make_request', return_value=mock_post_data) as mock_request:
            with patch.object(client_with_session, '_resolve_categories', return_value=[1, 2]):
                with patch.object(client_with_session, '_resolve_tags', return_value=[3, 4]):
                    result = await client_with_session.create_post(
                        title='Test Post',
                        content='Test content',
                        categories=['Category 1', 'Category 2'],
                        tags=['Tag 1', 'Tag 2']
                    )
                    
                    assert result == mock_post_data
                    mock_request.assert_called_once()
                    
                    # Check the data passed to _make_request
                    call_args = mock_request.call_args
                    assert call_args[0][0] == 'POST'  # method
                    assert call_args[0][1] == 'posts'  # endpoint
                    
                    post_data = call_args[1]['data']
                    assert post_data['title'] == 'Test Post'
                    assert post_data['content'] == 'Test content'
                    assert post_data['categories'] == [1, 2]
                    assert post_data['tags'] == [3, 4]
    
    @pytest.mark.asyncio
    async def test_get_posts(self, client_with_session):
        """Test retrieving posts."""
        mock_posts = [
            {'id': 1, 'title': {'rendered': 'Post 1'}},
            {'id': 2, 'title': {'rendered': 'Post 2'}}
        ]
        
        with patch.object(client_with_session, '_make_request', return_value=mock_posts) as mock_request:
            result = await client_with_session.get_posts(per_page=10, status='published')
            
            assert result == mock_posts
            mock_request.assert_called_once_with('GET', 'posts', params={
                'per_page': 10,
                'page': 1,
                'status': 'published'
            })
    
    @pytest.mark.asyncio
    async def test_update_post(self, client_with_session):
        """Test post update."""
        mock_updated_post = {
            'id': 1,
            'title': {'rendered': 'Updated Post'},
            'status': 'published'
        }
        
        with patch.object(client_with_session, '_make_request', return_value=mock_updated_post) as mock_request:
            result = await client_with_session.update_post(
                post_id=1,
                title='Updated Post',
                status='published'
            )
            
            assert result == mock_updated_post
            mock_request.assert_called_once_with('POST', 'posts/1', data={
                'title': 'Updated Post',
                'status': 'published'
            })
    
    @pytest.mark.asyncio
    async def test_delete_post(self, client_with_session):
        """Test post deletion."""
        mock_delete_response = {'deleted': True, 'previous': {'id': 1}}
        
        with patch.object(client_with_session, '_make_request', return_value=mock_delete_response) as mock_request:
            result = await client_with_session.delete_post(post_id=1, force=True)
            
            assert result == mock_delete_response
            mock_request.assert_called_once_with('DELETE', 'posts/1', params={'force': True})


class TestCommentManagement:
    """Test cases for comment management functionality."""
    
    @pytest.fixture
    def client_with_session(self, client):
        """Client with mocked session."""
        client.session = AsyncMock()
        client.auth_manager = AsyncMock()
        client.auth_manager.get_auth_headers.return_value = {'Authorization': 'Basic dGVzdA=='}
        return client
    
    @pytest.mark.asyncio
    async def test_create_comment(self, client_with_session):
        """Test comment creation."""
        mock_comment_data = {
            'id': 1,
            'post': 1,
            'content': {'rendered': 'Test comment'},
            'status': 'hold'
        }
        
        with patch.object(client_with_session, '_make_request', return_value=mock_comment_data) as mock_request:
            result = await client_with_session.create_comment(
                post_id=1,
                content='Test comment',
                author_name='Test Author'
            )
            
            assert result == mock_comment_data
            mock_request.assert_called_once()
            
            call_args = mock_request.call_args
            comment_data = call_args[1]['data']
            assert comment_data['post'] == 1
            assert comment_data['content'] == 'Test comment'
            assert comment_data['author_name'] == 'Test Author'
    
    @pytest.mark.asyncio
    async def test_moderate_comment(self, client_with_session):
        """Test comment moderation."""
        mock_moderated_comment = {
            'id': 1,
            'status': 'approved'
        }
        
        with patch.object(client_with_session, 'update_comment', return_value=mock_moderated_comment) as mock_update:
            result = await client_with_session.moderate_comment(comment_id=1, action='approved')
            
            assert result == mock_moderated_comment
            mock_update.assert_called_once_with(1, status='approved')
    
    @pytest.mark.asyncio
    async def test_moderate_comment_invalid_action(self, client_with_session):
        """Test comment moderation with invalid action."""
        with pytest.raises(CommentError, match="Invalid moderation action"):
            await client_with_session.moderate_comment(comment_id=1, action='invalid')


class TestAuthentication:
    """Test cases for authentication providers."""
    
    def test_basic_auth_provider(self):
        """Test Basic Auth provider."""
        provider = BasicAuthProvider('testuser', 'testpass')
        
        assert provider.is_valid()
        
        # Test auth headers
        headers = asyncio.run(provider.get_auth_headers())
        assert 'Authorization' in headers
        assert headers['Authorization'].startswith('Basic ')
    
    def test_application_password_provider(self):
        """Test Application Password provider."""
        provider = ApplicationPasswordProvider('testuser', 'app_password_123')
        
        assert provider.is_valid()
        
        # Test auth headers
        headers = asyncio.run(provider.get_auth_headers())
        assert 'Authorization' in headers
        assert headers['Authorization'].startswith('Basic ')
    
    @pytest.mark.asyncio
    async def test_jwt_auth_provider_refresh(self):
        """Test JWT Auth provider token refresh."""
        mock_jwt_response = {
            'token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************.test'
        }
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = mock_jwt_response
            mock_post.return_value.__aenter__.return_value = mock_response
            
            provider = JWTAuthProvider(
                'testuser',
                'testpass',
                'https://test.com/wp-json/jwt-auth/v1/token'
            )
            
            result = await provider.refresh_credentials()
            assert result is True
            assert provider.token == mock_jwt_response['token']
    
    def test_authentication_manager_basic(self):
        """Test Authentication Manager with Basic Auth."""
        api_config = {
            'auth_method': 'basic',
            'username': 'testuser',
            'password': 'testpass'
        }
        
        manager = AuthenticationManager(api_config)
        assert isinstance(manager.provider, BasicAuthProvider)
        assert manager.is_valid()


class TestMediaHandler:
    """Test cases for MediaHandler class."""
    
    @pytest.fixture
    def mock_client(self):
        """Mock WordPress client."""
        client = AsyncMock()
        client.config = {
            'media': {
                'max_file_size_mb': 50,
                'supported_image_formats': ['jpg', 'png', 'gif'],
                'image_optimization': {
                    'enabled': True,
                    'max_width': 1920,
                    'max_height': 1080,
                    'quality': 85
                }
            }
        }
        return client
    
    @pytest.fixture
    def media_handler(self, mock_client):
        """Create MediaHandler for testing."""
        with patch('utils.wordpress.media_handler.get_config', return_value={'wordpress': mock_client.config}):
            return MediaHandler(mock_client)
    
    def test_validate_file_size_valid(self, media_handler):
        """Test file size validation with valid size."""
        # 10MB file
        file_size = 10 * 1024 * 1024
        assert media_handler._validate_file_size(file_size) is True
    
    def test_validate_file_size_invalid(self, media_handler):
        """Test file size validation with invalid size."""
        # 100MB file (exceeds 50MB limit)
        file_size = 100 * 1024 * 1024
        
        with pytest.raises(ValidationError, match="File size.*exceeds limit"):
            media_handler._validate_file_size(file_size)
    
    def test_validate_file_format_image(self, media_handler):
        """Test file format validation for images."""
        assert media_handler._validate_file_format('test.jpg') == 'image'
        assert media_handler._validate_file_format('test.png') == 'image'
    
    def test_validate_file_format_unsupported(self, media_handler):
        """Test file format validation for unsupported format."""
        with pytest.raises(ValidationError, match="Unsupported file format"):
            media_handler._validate_file_format('test.xyz')
    
    @pytest.mark.asyncio
    async def test_upload_media(self, media_handler, mock_client):
        """Test media upload."""
        mock_upload_response = {
            'id': 1,
            'source_url': 'https://test.com/wp-content/uploads/test.jpg',
            'title': {'rendered': 'Test Image'}
        }
        
        mock_client._make_request.return_value = mock_upload_response
        
        # Mock image data
        test_image_data = b'fake_image_data'
        
        with patch.object(media_handler, '_optimize_image', return_value=test_image_data):
            result = await media_handler.upload_media(
                file_data=test_image_data,
                filename='test.jpg',
                title='Test Image'
            )
            
            assert result == mock_upload_response
            mock_client._make_request.assert_called_once()


class TestBatchOperations:
    """Test cases for batch operations."""
    
    @pytest.fixture
    def client_with_session(self, client):
        """Client with mocked session."""
        client.session = AsyncMock()
        client.auth_manager = AsyncMock()
        client.auth_manager.get_auth_headers.return_value = {'Authorization': 'Basic dGVzdA=='}
        return client
    
    @pytest.mark.asyncio
    async def test_create_posts_batch(self, client_with_session):
        """Test batch post creation."""
        posts_data = [
            {'title': 'Post 1', 'content': 'Content 1'},
            {'title': 'Post 2', 'content': 'Content 2'}
        ]
        
        mock_responses = [
            {'id': 1, 'title': {'rendered': 'Post 1'}},
            {'id': 2, 'title': {'rendered': 'Post 2'}}
        ]
        
        with patch.object(client_with_session, 'create_post', side_effect=mock_responses) as mock_create:
            results = await client_with_session.create_posts_batch(posts_data)
            
            assert len(results) == 2
            assert results == mock_responses
            assert mock_create.call_count == 2
    
    @pytest.mark.asyncio
    async def test_moderate_comments_batch(self, client_with_session):
        """Test batch comment moderation."""
        comment_ids = [1, 2, 3]
        mock_responses = [
            {'id': 1, 'status': 'approved'},
            {'id': 2, 'status': 'approved'},
            {'id': 3, 'status': 'approved'}
        ]
        
        with patch.object(client_with_session, 'moderate_comment', side_effect=mock_responses) as mock_moderate:
            results = await client_with_session.moderate_comments_batch(comment_ids, 'approved')
            
            assert len(results) == 3
            assert results == mock_responses
            assert mock_moderate.call_count == 3


class TestFactoryFunction:
    """Test cases for factory functions."""
    
    def test_create_wordpress_client(self):
        """Test WordPress client factory function."""
        mock_config = {
            'wordpress_apis': {
                'test': {
                    'api_url': 'https://test.example.com/wp-json/wp/v2/',
                    'username': 'test_user',
                    'password': 'test_pass'
                }
            },
            'wordpress': {}
        }
        
        with patch('utils.wordpress.wordpress_client.get_config', return_value=mock_config):
            client = create_wordpress_client(region_code='test')
            
            assert isinstance(client, WordPressClient)
            assert client.region_code == 'test'


# Test configuration for pytest
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()
