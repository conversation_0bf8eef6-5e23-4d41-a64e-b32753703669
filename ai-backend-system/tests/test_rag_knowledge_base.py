# ai-backend-system/tests/test_rag_knowledge_base.py
"""
RAG 知识库模块 (rag_knowledge_base) 的单元测试模块.

该模块包含了针对 rag_knowledge_base 目录下的所有模块 (scraper, indexer, retriever) 的单元测试用例.
使用 pytest 框架编写单元测试，验证 RAG 知识库模块的核心功能和流程是否正确.

测试用例包括：
- KnowledgeScraper 类的单元测试 (例如测试 search_product_reviews 方法, fetch_article_content 方法)
- KnowledgeIndexer 类的单元测试 (例如测试 create_vector_embedding 方法, index_document 方法, update_index 方法)
- KnowledgeRetriever 类的单元测试 (例如测试 retrieve_relevant_documents 方法, retrieve_documents_by_metadata_filter 方法)
- 模拟 RAG 知识库模块依赖的外部服务或库 (例如 Google Search API, Embedding 模型 API, Supabase 数据库)
- 验证 RAG 知识库模块方法的输入输出、异常处理和 side effects (例如是否正确调用外部 API, 是否正确读写数据库, 是否返回预期的检索结果)

测试套件可以使用 pytest 命令运行，例如在项目根目录下执行 `pytest tests/test_rag_knowledge_base.py` 命令.
"""
import pytest
from unittest.mock import MagicMock, patch # For mocking dependencies and patching

# Import RAG Knowledge Base modules to be tested
from rag_knowledge_base.scraper import KnowledgeScraper, create_knowledge_scraper
from rag_knowledge_base.indexer import KnowledgeIndexer, create_knowledge_indexer
from rag_knowledge_base.retriever import KnowledgeRetriever, create_knowledge_retriever

# Import DBConnector factory function for mocking dependency
from utils.db_connector import create_db_connector


# --- KnowledgeScraper tests ---
def test_knowledge_scraper_initialization():
    """
    测试 KnowledgeScraper 实例的初始化.
    验证 KnowledgeScraper 实例是否成功创建，以及 API 密钥和 CSE ID 是否正确设置.
    """
    google_search_api_key = "test_google_search_api_key" # (示例 Google Search API 密钥)
    google_cse_id = "test_google_cse_id" # (示例 Google CSE ID)
    scraper = create_knowledge_scraper(google_search_api_key, google_cse_id)
    assert isinstance(scraper, KnowledgeScraper)
    assert scraper.api_key == google_search_api_key
    assert scraper.cse_id == google_cse_id

@patch('rag_knowledge_base.scraper.requests.get') # 使用 patch 装饰器 Mock requests.get
def test_knowledge_scraper_search_product_reviews(mock_get):
    """
    测试 KnowledgeScraper 的 search_product_reviews 方法.
    验证该方法是否能正确调用 Google Search API 搜索商品评测文章，并返回搜索结果.
    使用 mock_get 对象 Mock requests.get 方法，模拟 API 请求和响应.
    """
    google_search_api_key = "test_google_search_api_key"
    google_cse_id = "test_google_cse_id"
    scraper = create_knowledge_scraper(google_search_api_key, google_cse_id)
    product_name = "Bose QC45" # (示例商品名称)
    mock_response = MagicMock() # 创建 MagicMock 对象模拟 requests.Response
    mock_response.status_code = 200 # 模拟 API 成功响应状态码 200 (OK)
    mock_response.json.return_value = {"items": []} # 模拟 API 响应 JSON 数据 (空结果)
    mock_get.return_value = mock_response # 设置 mock_get 的返回值

    # search_results = scraper.search_product_reviews(product_name) # 调用 search_product_reviews 方法
    # assert isinstance(search_results, list) # 验证返回结果是否为列表类型
    # mock_get.assert_called_once() # 验证 requests.get 方法是否被调用一次

@patch('rag_knowledge_base.scraper.requests.get') # 使用 patch 装饰器 Mock requests.get
def test_knowledge_scraper_fetch_article_content(mock_get):
    """
    测试 KnowledgeScraper 的 fetch_article_content 方法.
    验证该方法是否能正确抓取评测文章 HTML 内容.
    使用 mock_get 对象 Mock requests.get 方法，模拟 API 请求和响应.
    """
    google_search_api_key = "test_google_search_api_key"
    google_cse_id = "test_google_cse_id"
    scraper = create_knowledge_scraper(google_search_api_key, google_cse_id)
    article_url = "http://test-review-site.com/review-article" # (示例评测文章 URL)
    mock_response = MagicMock() # 创建 MagicMock 对象模拟 requests.Response
    mock_response.status_code = 200 # 模拟 API 成功响应状态码 200 (OK)
    mock_response.text = "<html<body>Test Article Content</body></html>" # 模拟 API 响应 HTML 内容
    mock_get.return_value = mock_response # 设置 mock_get 的返回值

    # article_content = scraper.fetch_article_content(article_url) # 调用 fetch_article_content 方法
    # assert "Test Article Content" in article_content # 验证返回的 HTML 内容是否包含预期文本
    # mock_get.assert_called_once() # 验证 requests.get 方法是否被调用一次


# --- KnowledgeIndexer tests ---
def test_knowledge_indexer_initialization():
    """
    测试 KnowledgeIndexer 实例的初始化.
    验证 KnowledgeIndexer 实例是否成功创建，以及 DBConnector 是否正确注入.
    需要 Mock DBConnector 实例.
    """
    mock_db_connector = create_db_connector("https://test-project-id.supabase.co", "test_supabase_key") # Mock DBConnector
    indexer = create_knowledge_indexer(mock_db_connector)
    assert isinstance(indexer, KnowledgeIndexer)
    assert indexer.db_connector is not None # 验证 DBConnector 是否成功注入

def test_knowledge_indexer_create_vector_embedding():
    """
    测试 KnowledgeIndexer 的 create_vector_embedding 方法.
    验证该方法是否能正确创建文本内容的向量 Embedding.
    需要 Mock Embedding 模型 API 的调用，模拟 API 请求和响应.
    """
    mock_db_connector = create_db_connector("https://test-project-id.supabase.co", "test_supabase_key") # Mock DBConnector
    indexer = create_knowledge_indexer(mock_db_connector)
    text_content = "This is a test review article." # (示例文本内容)
    # embedding_vector = indexer.create_vector_embedding(text_content)
    # assert isinstance(embedding_vector, list) # 验证返回结果是否为列表类型 (向量)
    # assert len(embedding_vector) > 0 # 验证向量维度是否大于 0 (非空向量)
    pass

@patch('rag_knowledge_base.indexer.KnowledgeIndexer.create_vector_embedding') # 使用 patch.object 装饰器 Mock create_vector_embedding 方法
def test_knowledge_indexer_index_document(mock_create_embedding):
    """
    测试 KnowledgeIndexer 的 index_document 方法.
    验证该方法是否能正确索引文档，创建向量 Embedding 并存储到向量数据库.
    需要 Mock create_vector_embedding 方法和 DBConnector 的 API 调用，模拟 Embedding 创建和数据库写入过程.
    """
    mock_db_connector = create_db_connector("https://test-project-id.supabase.co", "test_supabase_key") # Mock DBConnector
    indexer = create_knowledge_indexer(mock_db_connector)
    document_content = "Test document content for indexing." # (示例文档内容)
    document_metadata = {} # (示例文档元数据)
    mock_create_embedding.return_value = [0.1, 0.2, 0.3] # 模拟 create_vector_embedding 方法返回示例向量
    mock_db_connector.insert_data = MagicMock() # Mock DBConnector 的 insert_data 方法

    # indexer.index_document(document_content, document_metadata) # 调用 index_document 方法
    # mock_create_embedding.assert_called_once_with(document_content) # 验证 create_vector_embedding 方法是否被调用，并传入正确的文本内容
    # mock_db_connector.insert_data.assert_called_once() # 验证 DBConnector 的 insert_data 方法是否被调用


# --- KnowledgeRetriever tests ---
def test_knowledge_retriever_initialization():
    """
    测试 KnowledgeRetriever 实例的初始化.
    验证 KnowledgeRetriever 实例是否成功创建，以及 DBConnector 是否正确注入.
    需要 Mock DBConnector 实例.
    """
    mock_db_connector = create_db_connector("https://test-project-id.supabase.co", "test_supabase_key") # Mock DBConnector
    retriever = create_knowledge_retriever(mock_db_connector)
    assert isinstance(retriever, KnowledgeRetriever)
    assert retriever.db_connector is not None # 验证 DBConnector 是否成功注入

def test_knowledge_retriever_retrieve_relevant_documents():
    """
    测试 KnowledgeRetriever 的 retrieve_relevant_documents 方法.
    验证该方法是否能正确检索相关文档，从向量数据库中获取.
    需要 Mock DBConnector 的 query_vector_db 方法，模拟向量数据库查询和结果返回.
    """
    mock_db_connector = create_db_connector("https://test-project-id.supabase.co", "test_supabase_key") # Mock DBConnector
    retriever = create_knowledge_retriever(mock_db_connector)
    query_text = "Best noise cancelling headphones" # (示例查询文本)
    mock_db_connector.query_vector_db = MagicMock(return_value=[]) # Mock query_vector_db 方法，模拟返回空结果
    # retrieved_documents = retriever.retrieve_relevant_documents(query_text)
    # assert isinstance(retrieved_documents, list) # 验证返回结果是否为列表类型
    # mock_db_connector.query_vector_db.assert_called_once() # 验证 query_vector_db 方法是否被调用


def test_knowledge_retriever_retrieve_documents_by_metadata_filter():
    """
    测试 KnowledgeRetriever 的 retrieve_documents_by_metadata_filter 方法.
    验证该方法是否能正确根据元数据过滤器检索相关文档.
    需要 Mock DBConnector 的 query_vector_db 方法，模拟向量数据库查询和结果返回.
    """
    mock_db_connector = create_db_connector("https://test-project-id.supabase.co", "test_supabase_key") # Mock DBConnector
    retriever = create_knowledge_retriever(mock_db_connector)
    query_text = "Headphones with long battery life" # (示例查询文本)
    metadata_filter = {"source_type": "review_article"} # (示例元数据过滤器)
    mock_db_connector.query_vector_db = MagicMock(return_value=[]) # Mock query_vector_db 方法，模拟返回空结果
    # retrieved_documents = retriever.retrieve_documents_by_metadata_filter(query_text, metadata_filter)
    # assert isinstance(retrieved_documents, list) # 验证返回结果是否为列表类型
    # mock_db_connector.query_vector_db.assert_called_once() # 验证 query_vector_db 方法是否被调用


# 可以根据需要添加更多 RAG 知识库模块的单元测试用例，覆盖模块的所有核心功能和流程.