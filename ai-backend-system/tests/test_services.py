# ai-backend-system/tests/test_services.py
"""
Service 模块 (services) 的单元测试模块.

该模块包含了针对 services 目录下的所有 Service 类 (DealService, ArticleService) 的单元测试用例.
使用 pytest 框架编写单元测试，验证 Service 类的核心业务流程编排和功能是否正确.

测试用例包括：
- Service 类的初始化测试 (例如测试 DealService 实例是否成功创建，依赖的 Agent 和 Util 是否正确注入)
- Service 类的核心业务流程方法的功能测试 (例如测试 DealService 的 automate_deal_crawl_and_publish 方法, ArticleService 的 generate_and_publish_review_article 方法等)
- 模拟 Service 类依赖的 Agent 和 Util 工具 (例如使用 mock 对象或 unittest.mock 库)
- 验证 Service 方法的输入输出、状态变化和业务逻辑 (例如是否正确调用 Agent 和 Util, 是否按照预期流程执行业务逻辑)
- 集成测试 Service 类与 Agent 类、Util 类的协作 (可选，可以放在集成测试模块中)

测试套件可以使用 pytest 命令运行，例如在项目根目录下执行 `pytest tests/test_services.py` 命令.
"""
import pytest
from unittest.mock import MagicMock # For mocking dependencies

# Import Service classes to be tested
from services.deal_service import DealService, create_deal_service
from services.article_service import ArticleService, create_article_service

# Import Agent and Util factory functions for mocking dependencies
from agents.operational_manager_agent import create_operational_manager_agent
from agents.data_processing_agent import create_data_processing_agent
from agents.content_generation_agent import create_content_generation_agent
from agents.content_review_agent import create_content_review_agent # Although not directly used in services yet, for completeness
from utils.scrapy_util import create_scrapy_util
from utils.data_processing_util import create_data_processing_util
from utils.wordpress_util import create_wordpress_util
from utils.db_connector import create_db_connector
from rag_knowledge_base.retriever import create_knowledge_retriever # Assuming ArticleService uses it


# --- DealService tests ---
def test_deal_service_initialization():
    """
    测试 DealService 实例的初始化.
    验证 DealService 实例是否成功创建，以及依赖的 Agent 和 Util 是否正确注入.
    需要 Mock 依赖的 Agent 和 Util 实例.
    """
    mock_operational_manager_agent = create_operational_manager_agent() # Mock OperationalManagerAgent
    mock_data_processing_agent = create_data_processing_agent() # Mock DataProcessingAgent
    mock_content_generation_agent = create_content_generation_agent() # Mock ContentGenerationAgent
    mock_scrapy_util = create_scrapy_util() # Mock ScrapyUtil
    mock_data_processing_util = create_data_processing_util() # Mock DataProcessingUtil
    mock_wordpress_util = create_wordpress_util("http://test-wordpress-site.com/wp-json/wp/v2/") # Mock WordPressUtil
    mock_db_connector = create_db_connector("https://test-project-id.supabase.co", "test_supabase_key") # Mock DBConnector

    service = create_deal_service(
        operational_manager_agent=mock_operational_manager_agent,
        data_processing_agent=mock_data_processing_agent,
        content_generation_agent=mock_content_generation_agent,
        scrapy_util=mock_scrapy_util,
        data_processing_util=mock_data_processing_util,
        wordpress_util=mock_wordpress_util,
        db_connector=mock_db_connector
    )
    assert isinstance(service, DealService)
    assert service.operational_manager_agent is not None # 验证 Agent 和 Util 是否成功注入
    assert service.scrapy_util is not None

def test_deal_service_automate_deal_crawl_and_publish():
    """
    测试 DealService 的 automate_deal_crawl_and_publish 方法.
    验证该方法是否能正确 orchestrate Deal 自动化抓取和发布流程，并调用相关的 Agent 和 Util.
    需要 Mock 依赖的 Agent 和 Util 实例，并验证它们是否被正确调用.
    """
    mock_operational_manager_agent = create_operational_manager_agent() # Mock OperationalManagerAgent
    mock_data_processing_agent = create_data_processing_agent() # Mock DataProcessingAgent
    mock_content_generation_agent = create_content_generation_agent() # Mock ContentGenerationAgent
    mock_scrapy_util = create_scrapy_util() # Mock ScrapyUtil
    mock_data_processing_util = create_data_processing_util() # Mock DataProcessingUtil
    mock_wordpress_util = create_wordpress_util("http://test-wordpress-site.com/wp-json/wp/v2/") # Mock WordPressUtil
    mock_db_connector = create_db_connector("https://test-project-id.supabase.co", "test_supabase_key") # Mock DBConnector

    service = create_deal_service(
        operational_manager_agent=mock_operational_manager_agent,
        data_processing_agent=mock_data_processing_agent,
        content_generation_agent=mock_content_generation_agent,
        scrapy_util=mock_scrapy_util,
        data_processing_util=mock_data_processing_util,
        wordpress_util=mock_wordpress_util,
        db_connector=mock_db_connector
    )
    deal_source_name = "test_source" # (示例 Deal 来源平台名称)
    # service.automate_deal_crawl_and_publish(deal_source_name)
    # (验证相关的 Agent 和 Util Mock 对象是否被正确调用，例如 mock_scrapy_util.run_spider 是否被调用, mock_data_processing_agent.process_raw_deal_data 是否被调用等)
    pass


# --- ArticleService tests ---
def test_article_service_initialization():
    """
    测试 ArticleService 实例的初始化.
    """
    mock_content_generation_agent = create_content_generation_agent() # Mock ContentGenerationAgent
    mock_wordpress_util = create_wordpress_util("http://test-wordpress-site.com/wp-json/wp/v2/") # Mock WordPressUtil
    mock_db_connector = create_db_connector("https://test-project-id.supabase.co", "test_supabase_key") # Mock DBConnector
    mock_knowledge_retriever = create_knowledge_retriever(mock_db_connector) # Mock KnowledgeRetriever

    service = create_article_service(
        content_generation_agent=mock_content_generation_agent,
        wordpress_util=mock_wordpress_util,
        db_connector=mock_db_connector,
        knowledge_retriever=mock_knowledge_retriever
    )
    assert isinstance(service, ArticleService)
    assert service.content_generation_agent is not None
    assert service.wordpress_util is not None

def test_article_service_generate_and_publish_review_article():
    """
    测试 ArticleService 的 generate_and_publish_review_article 方法.
    验证该方法是否能正确 orchestrate 评测文章生成和发布流程.
    """
    mock_content_generation_agent = create_content_generation_agent() # Mock ContentGenerationAgent
    mock_wordpress_util = create_wordpress_util("http://test-wordpress-site.com/wp-json/wp/v2/") # Mock WordPressUtil
    mock_db_connector = create_db_connector("https://test-project-id.supabase.co", "test_supabase_key") # Mock DBConnector
    mock_knowledge_retriever = create_knowledge_retriever(mock_db_connector) # Mock KnowledgeRetriever

    service = create_article_service(
        content_generation_agent=mock_content_generation_agent,
        wordpress_util=mock_wordpress_util,
        db_connector=mock_db_connector,
        knowledge_retriever=mock_knowledge_retriever
    )
    product_info = {} # (示例商品信息)
    # service.generate_and_publish_review_article(product_info)
    # (验证相关的 Agent 和 Util Mock 对象是否被正确调用，例如 mock_content_generation_agent.generate_review_article_outline 是否被调用, mock_wordpress_util.publish_review_article 是否被调用等)
    pass


# 可以根据需要添加更多 Service 类的单元测试用例，覆盖 Service 类的所有核心业务流程和功能.