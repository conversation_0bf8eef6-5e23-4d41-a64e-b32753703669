# ai-backend-system/config/__init__.py
"""
config 包的初始化模块 - **已更新，支持多区域配置加载**.

该模块负责加载和管理系统配置参数，并增强了对多区域配置的加载和访问支持.
现在可以加载和访问多区域 WordPress API 配置和多区域爬虫配置.
"""
import os
import yaml
from typing import Dict, Any

class ConfigLoader:
    """
    配置加载器 (ConfigLoader) 类，负责加载和管理系统配置 - **已更新，支持多区域配置加载**.
    """
    def __init__(self, config_file_path: str = "config/config.yaml"):
        """
        初始化 ConfigLoader 实例 - **已更新，支持多区域配置加载**.
        """
        self.config_file_path = config_file_path
        self.config_data: Dict[str, Any] = self._load_config() # 加载配置文件数据

    def _load_config(self) -> Dict[str, Any]:
        """
        加载 YAML 配置文件 - **已更新，支持多区域配置加载**.
        """
        try:
            with open(self.config_file_path, 'r') as f:
                config = yaml.safe_load(f) # 使用 yaml.safe_load 安全加载 YAML 文件
            return config or {} # 如果配置文件为空，返回空字典
        except FileNotFoundError:
            print(f"配置文件未找到: {self.config_file_path}")
            return {} # 配置文件未找到时返回空字典
        except yaml.YAMLError as e:
            print(f"配置文件解析错误: {self.config_file_path} - {e}")
            return {} # 配置文件解析错误时返回空字典

    def get_config(self) -> Dict[str, Any]:
        """
        获取所有配置参数 - **已更新，支持多区域配置加载**.
        """
        return self.config_data

    def get_api_keys(self) -> Dict[str, str]:
        """
        获取 API 密钥配置 - **已更新，支持多区域配置加载**.
        """
        return self.config_data.get("api_keys", {})

    def get_wordpress_api_configs(self) -> Dict[str, Dict[str, str]]: # **[新增]** 获取多区域 WordPress API 配置
        """
        获取多区域 WordPress API 配置.

        Returns:
            Dict[str, Dict[str, str]]: 包含多区域 WordPress API 配置的字典.
                                         字典的键为区域代码，值为该区域的 API 配置字典.
        """
        return self.config_data.get("wordpress_apis", {}) # Changed to get "wordpress_apis"

    def get_crawl_configs(self) -> Dict[str, Dict[str, Any]]: # **[新增]** 获取多区域爬虫配置
        """
        获取多区域爬虫配置.

        Returns:
            Dict[str, Dict[str, Any]]: 包含多区域爬虫配置的字典.
                                         字典的键为区域代码，值为该区域的爬虫配置字典.
        """
        return self.config_data.get("crawl_configs", {}) # Changed to get "crawl_configs"


    def get_supabase_config(self) -> Dict[str, str]:
        """
        获取 Supabase 数据库连接配置 - **已更新，支持多区域配置加载**.
        """
        return self.config_data.get("supabase", {})

    def get_llm_model_config(self) -> Dict[str, Any]:
        """
        获取 LLM 模型配置 - **已更新，支持多区域配置加载**.
        """
        return self.config_data.get("llm_models", {})

    def get_scrapy_config(self) -> Dict[str, Any]:
        """
        获取 Scrapy 爬虫配置 (通用配置，非区域特定) - **已更新，支持多区域配置加载**.
        """
        return self.config_data.get("scrapy", {})

    def get_logging_config(self) -> Dict[str, Any]:
        """
        获取日志配置 - **已更新，支持多区域配置加载**.
        """
        return self.config_data.get("logging", {})

    def get_google_search_api_config(self) -> Dict[str, str]:
        """
        获取 Google Search API 配置 - **已更新，支持多区域配置加载**.
        """
        return self.config_data.get("google_search_api", {})

    def get_rag_knowledge_base_config(self) -> Dict[str, Any]:
        """
        获取 RAG 知识库配置 - **已更新，支持多区域配置加载**.
        """
        return self.config_data.get("rag_knowledge_base", {})

# 创建全局 ConfigLoader 实例
config_loader = ConfigLoader()

# ... (全局函数 get_config(), get_api_keys(), etc. - no changes needed as they use the global config_loader instance)

def get_wordpress_api_configs(): # **[新增]** 全局函数，获取多区域 WordPress API 配置
    """
    全局函数，获取多区域 WordPress API 配置 (通过全局 ConfigLoader 实例).
    """
    return config_loader.get_wordpress_api_configs()

def get_crawl_configs(): # **[新增]** 全局函数，获取多区域爬虫配置
    """
    全局函数，获取多区域爬虫配置 (通过全局 ConfigLoader 实例).
    """
    return config_loader.get_crawl_configs()

def get_config():
    """获取配置信息"""
    config_path = os.path.join(os.path.dirname(__file__), 'config.yaml')
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)