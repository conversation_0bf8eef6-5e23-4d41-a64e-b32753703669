# ai-backend-system/main.py
"""
AI 后端系统 (ai-backend-system) 的程序入口模块 (main.py) - **已更新，支持多区域运营**.

该模块是 AI 后端系统的启动点，负责：
1. 加载系统配置 (从 config/config.yaml 文件) - **已更新，支持多区域配置加载**.
2. 初始化各个核心组件 (Agents, Utils, Services, RAG Knowledge Base 模块) - **已更新，部分 Util 初始化支持多区域配置**.
3. 启动任务调度器 (例如定时执行 Deal 抓取任务, 知识库更新任务) - 可选，初期可以手动触发任务.
4. 启动 API Server (如果需要对外提供 API 接口) - 可选，初期可能不需要对外提供 API.
5. 配置和初始化日志系统 (根据 config/config.yaml 中的日志配置).
6. 提供程序入口函数 (例如 run_deal_crawl_flow, generate_review_article 等)，方便手动触发业务流程或集成到其他系统 - **已更新，程序入口函数支持指定区域代码**.

程序启动流程 (已更新，支持多区域):
1. 运行 `python main.py` 命令或通过任务调度系统触发.
2. `main.py` 模块被执行，首先加载配置文件 (config/config.yaml)，包括多区域配置.
3. 初始化 ConfigLoader 实例，读取和解析配置文件内容，包括多区域 WordPress API 和爬虫配置.
4. 根据配置文件内容，创建和初始化各个 Agent, Util, Service 和 RAG Knowledge Base 模块的实例 - **Utils 初始化时传入多区域配置**.
5. (可选) 配置任务调度器，并注册需要定时执行的**区域化任务** (例如定时执行澳洲区域 Deal 抓取任务, 德国区域 Deal 抓取任务，知识库更新任务等).
6. (可选) 启动 API Server，监听指定的端口，等待 API 请求 (如果需要对外提供 API 接口).
7. 系统进入运行状态，等待任务触发或 API 请求，**支持根据区域代码执行不同的业务流程**.
"""
import logging
import schedule
import time
# from fastapi import FastAPI # Example for FastAPI if API is needed

# Import ConfigLoader to load configurations, including multi-region configs
from config import ConfigLoader, get_config, get_logging_config, get_wordpress_api_configs, get_crawl_configs, get_supabase_config, get_api_keys, get_google_search_api_config, get_llm_model_config, get_scrapy_config, get_rag_knowledge_base_config

# Import Agents factory functions
from agents.operational_manager_agent import create_operational_manager_agent
from agents.data_processing_agent import create_data_processing_agent
from agents.content_generation_agent import create_content_generation_agent
from agents.content_review_agent import create_content_review_agent

# Import Util factory functions
from utils.scrapy_util import create_scrapy_util
from utils.data_processing_util import create_data_processing_util
from utils.wordpress_util import create_wordpress_util
from utils.db_connector import create_db_connector

# Import Service factory functions
from services.deal_service import create_deal_service
from services.article_service import create_article_service

# Import RAG Knowledge Base factory functions
from rag_knowledge_base.scraper import create_knowledge_scraper
from rag_knowledge_base.indexer import create_knowledge_indexer
from rag_knowledge_base.retriever import create_knowledge_retriever

# Initialize logging system based on config
logging_config = get_logging_config()
log_level = logging_config.get("log_level", "INFO").upper() # Default to INFO level if not configured
log_file_path = logging_config.get("log_file_path", "logs/ai_backend_system.log") # Default log file path
log_format = logging_config.get("log_format", "%(asctime)s - %(levelname)s - %(module)s - %(message)s") # Default log format

logging.basicConfig(level=log_level, filename=log_file_path, format=log_format) # Configure basic logging

logger = logging.getLogger(__name__) # Get logger instance for this module
logger.info("Starting AI Backend System...") # Log system startup message

# Load configurations using ConfigLoader - **Loading multi-region configs now**
config = get_config() # Load all configurations
api_keys_config = get_api_keys()
supabase_config = get_supabase_config()
wordpress_api_configs = get_wordpress_api_configs() # **[New]** Load multi-region WordPress API configurations
crawl_configs = get_crawl_configs() # **[New]** Load multi-region crawl configurations
google_search_api_config = get_google_search_api_config()
llm_model_config = get_llm_model_config()
scrapy_config = get_scrapy_config()
rag_kb_config = get_rag_knowledge_base_config()

# --- Initialize Utils --- - **Passing multi-region configs to Utils**
logger.info("Initializing Utils...")
scrapy_util = create_scrapy_util(crawl_configs=crawl_configs) # Create ScrapyUtil instance, passing multi-region crawl configs
data_processing_util = create_data_processing_util()
wordpress_util = create_wordpress_util(wordpress_api_configs=wordpress_api_configs) # Create WordPressUtil instance, passing multi-region API configs
db_connector = create_db_connector(
    supabase_url=supabase_config.get("supabase_url"),
    supabase_key=supabase_config.get("supabase_key")
)
logger.info("Utils initialized.")

# --- Initialize RAG Knowledge Base Modules --- (No changes needed for multi-region in initialization)
logger.info("Initializing RAG Knowledge Base Modules...")
knowledge_scraper = create_knowledge_scraper(
    google_search_api_key=google_search_api_config.get("api_key"),
    google_cse_id=google_search_api_config.get("cse_id")
)
knowledge_indexer = create_knowledge_indexer(db_connector=db_connector)
knowledge_retriever = create_knowledge_retriever(db_connector=db_connector)
logger.info("RAG Knowledge Base Modules initialized.")

# --- Initialize Agents --- (No changes needed for multi-region in initialization)
logger.info("Initializing Agents...")
llm_config_for_agents = {
    "api_key": api_keys_config.get("openai_api_key"),
    "model_name": llm_model_config.get("default_model_name"),
    "temperature": llm_model_config.get("model_options", {}).get("temperature"),
    "max_tokens": llm_model_config.get("model_options", {}).get("max_tokens"),
}
operational_manager_agent = create_operational_manager_agent(llm_config=llm_config_for_agents)
data_processing_agent = create_data_processing_agent(llm_config=llm_config_for_agents)
content_generation_agent = create_content_generation_agent(llm_config=llm_config_for_agents)
content_review_agent = create_content_review_agent(llm_config=llm_config_for_agents)
logger.info("Agents initialized.")

# --- Initialize Services --- (No changes needed for multi-region in initialization)
logger.info("Initializing Services...")
deal_service = create_deal_service(
    operational_manager_agent=operational_manager_agent,
    data_processing_agent=data_processing_agent,
    content_generation_agent=content_generation_agent,
    scrapy_util=scrapy_util,
    data_processing_util=data_processing_util,
    wordpress_util=wordpress_util,
    db_connector=db_connector
)
article_service = create_article_service(
    content_generation_agent=content_generation_agent,
    wordpress_util=wordpress_util,
    db_connector=db_connector,
    knowledge_retriever=knowledge_retriever
)
logger.info("Services initialized.")

# --- (Optional) Task Scheduling --- (Example updated to potentially handle region-specific tasks)
def scheduled_deal_crawl_task_au(): # Example: Scheduled task for AU region
    """
    示例定时任务: 启动 **澳洲区域** Deal 信息自动化抓取流程 (示例，需要根据实际需求完善).
    """
    logger.info("Running scheduled Deal crawl task for **AU region**...") # Region-specific log message
    deal_source = "example_ecommerce_site_au" # (示例 Deal 来源平台名称 for AU region)
    region_code = "au" # **[New]** Define region code for AU task
    deal_service.automate_deal_crawl_and_publish(deal_source_name=deal_source, region_code=region_code) # Pass region_code
    logger.info("Scheduled Deal crawl task for **AU region** finished.")

def scheduled_deal_crawl_task_de(): # Example: Scheduled task for DE region
    """
    示例定时任务: 启动 **德国区域** Deal 信息自动化抓取流程 (示例，需要根据实际需求完善).
    """
    logger.info("Running scheduled Deal crawl task for **DE region**...") # Region-specific log message
    deal_source = "example_ecommerce_site_de" # (示例 Deal 来源平台名称 for DE region)
    region_code = "de" # **[New]** Define region code for DE task
    deal_service.automate_deal_crawl_and_publish(deal_source_name=deal_source, region_code=region_code) # Pass region_code
    logger.info("Scheduled Deal crawl task for **DE region** finished.")


# schedule.every().day.at("10:00").do(scheduled_deal_crawl_task_au) # Example: Schedule AU Deal crawl task daily at 10:00 AM
# schedule.every().day.at("11:00").do(scheduled_deal_crawl_task_de) # Example: Schedule DE Deal crawl task daily at 11:00 AM
# logger.info("Deal crawl tasks scheduled for AU and DE regions.")


# --- (Optional) API Server Startup (Example using FastAPI - uncomment if needed) --- (API Server example remains the same framework)
# app = FastAPI() # Create FastAPI app instance
#
# @app.get("/api/deals/crawl") # Example API endpoint to trigger Deal crawl manually
# async def trigger_deal_crawl_api(deal_source_name: str):
#     """
#     Example API endpoint to manually trigger Deal crawl flow.
#     """
#     logger.info(f"API endpoint /api/deals/crawl triggered for source: {deal_source_name}")
#     deal_service.automate_deal_crawl_and_publish(deal_source_name=deal_source_name)
#     return {"message": f"Deal crawl task started for source: {deal_source_name}"}
#
#
# def start_api_server():
#     """
#     启动 API Server (使用 Uvicorn).
#     """
#     import uvicorn
#     api_host = "0.0.0.0" # Listen on all interfaces
#     api_port = 8000 # Example API port
#     logger.info(f"Starting API server on http://{api_host}:{api_host}")
#     uvicorn.run(app, host=api_host, port=api_port)
#
# import threading # For running API server in a separate thread
# api_thread = threading.Thread(target=start_api_server) # Create thread for API server
# api_thread.start() # Start API server thread
# logger.info("API server thread started.")


# --- Main entry point for manual task execution --- (Updated to include region_code)
def run_deal_crawl_flow(deal_source_name: str, region_code: str): # **Updated to accept region_code**
    """
    程序入口函数: 手动触发 Deal 信息自动化抓取和发布流程 - **已更新，支持指定区域**.

    Args:
        deal_source_name (str): Deal 来源平台名称.
        region_code (str): 目标区域代码 (例如 "au", "de", "us"). # **[New]** Region code parameter
    """
    logger.info(f"Manual Deal crawl flow triggered for source: {deal_source_name}, region: {region_code}") # Log region code
    deal_service.automate_deal_crawl_and_publish(deal_source_name=deal_source_name, region_code=region_code) # Pass region_code
    logger.info(f"Manual Deal crawl flow for source: {deal_source_name}, region: {region_code} started.") # Log region code


def generate_review_article_example(product_name: str): # **No region code added here, can be added if needed for region-aware article generation**
    """
    程序入口函数: 手动触发评测文章生成流程 (示例) - **No region code added in this example, can be added if needed**.

    Args:
        product_name (str): 商品名称，用于指定要评测的商品.
    """
    logger.info(f"Manual review article generation triggered for product: {product_name}")
    product_info = {"product_name": product_name} # (示例商品信息，actual info should be from DB/API)
    article_service.generate_and_publish_review_article(product_info=product_info)
    logger.info(f"Manual review article generation for product: {product_name} started.")


if __name__ == "__main__":
    logger.info("AI Backend System is ready (Multi-Region Enabled).") # Updated log message for multi-region
    # --- Example Usage (uncomment to run specific tasks manually) ---

    # Example 1: Run Deal crawl flow manually for "amazon_au" source in "au" region - **Example updated to include region_code**
    run_deal_crawl_flow(deal_source_name="amazon_au", region_code="au") # **[New]** Example with region_code

    # Example 2: Run Deal crawl flow manually for "amazon_de" source in "de" region - **[New]** Example for DE region
    # run_deal_crawl_flow(deal_source_name="amazon_de", region_code="de")

    # Example 3: Generate review article for "Bose QuietComfort 45" (region-agnostic for now)
    # generate_review_article_example(product_name="Bose QuietComfort 45")

    # --- (Optional) Start Scheduler (uncomment to enable task scheduling) ---
    # while True:
    #     schedule.run_pending()
    #     time.sleep(1)