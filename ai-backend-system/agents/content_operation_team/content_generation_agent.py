# ai-backend-system/agents/content_operation_team/content_generation_agent.py
"""
定义内容生成专员 Agent (ContentGenerationAgent) 类。

该 Agent 负责：
- 接收运营经理 Agent (OperationalManagerAgent) 或其他 Agent 发送的内容生成任务。
- 调用 LLM 模型 (通过配置的 LLM API) 生成平台所需的内容，例如 Deal 描述、评测文章大纲、评测文章内容等。
- 调用 WordPress Util (WordPressUtil) 获取 WordPress 网站上的内容 (例如用户评论)，进行必要的互动 (例如回复用户评论)。
- 根据内容评审专员 Agent (ContentReviewAgent) 的反馈，对生成内容进行完善和优化。
- 调用 WordPress Util (WordPressUtil) 将最终 AI 生成内容提交到 WordPress 网站。

核心功能包括：
- AI 生成 Deal 描述 (根据商品信息和 Deal 信息)
- AI 生成评测文章大纲 (为评测文章创作提供结构框架)
- AI 生成评测文章内容 (根据评测文章大纲和商品信息)
- 与用户评论互动 (例如根据用户评论内容生成回复)
- 根据评审反馈完善生成内容 (根据内容评审专员 Agent 的评审意见进行内容迭代)
"""
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
import os
import dotenv
from abc import ABC, abstractmethod
dotenv.load_dotenv()

class ContentGenerationAgent(ABC):
    """
    内容生成专员 Agent 抽象类, 负责生成 Deal，Comments 和 Articles 等平台内容。
    """
    def __init__(self, name="content_generator", llm_config=None):
        """
        初始化方法。

        Args:
            name (str, optional): Agent 的名称，默认为 "content_generator"。
            llm_config (dict, optional): LLM 配置字典，用于配置 Agent 使用的 LLM 模型。
        """
        # 创建基于 AssistantAgent 的实例
        self.agent = AssistantAgent(
            name=name,
            model_client=OpenAIChatCompletionClient(**llm_config) if llm_config else None,
            description=self.get_description()
        )
    
    @abstractmethod
    def get_description(self):
        """返回该Agent的描述"""
        pass
    
    @abstractmethod
    def generate_content(self, *args, **kwargs):
        """抽象方法，不同类型的内容生成Agent需要实现该方法"""
        pass

    def refine_content_based_on_feedback(self, content: str, feedback: str):
        """
        根据评审反馈完善和优化已生成的内容 (Deal 描述或评测文章)。

        Args:
            content (str): 已生成的内容 (Deal 描述或评测文章，文本字符串)。
            feedback (str): 内容评审专员 Agent 提供的评审反馈意见 (文本字符串)。

        Returns:
            str: 优化后的内容 (文本字符串)。
        """
        pass


class DealGenerationAgent(ContentGenerationAgent):
    """负责生成Deal描述的Agent"""
    
    def get_description(self):
        return "负责生成Deal描述的内容生成专员Agent"
    
    def generate_content(self, product_info: dict, deal_info: dict):
        """实现抽象方法，生成Deal描述"""
        return self.generate_deal_description(product_info, deal_info)
        
    def generate_deal_description(self, product_info: dict, deal_info: dict):
        """
        AI 生成 Deal 描述内容。

        Args:
            product_info (dict): 商品信息，包含商品名称、品牌、主要参数等。
            deal_info (dict): Deal 信息，包含 Deal 价格、折扣力度、有效期、Deal 链接等。

        Returns:
            str: AI 生成的 Deal 描述内容 (文本字符串)。
        """
        pass


class CommentGenerationAgent(ContentGenerationAgent):
    """负责生成评论回复的Agent"""
    
    def get_description(self):
        return "负责生成评论回复的内容生成专员Agent"
    
    def generate_content(self, comment_content: str, deal_id: int):
        """实现抽象方法，生成评论回复"""
        return self.interact_with_user_comment(comment_content, deal_id)
        
    def interact_with_user_comment(self, comment_content: str, deal_id: int):
        """
        与用户评论互动，例如回复用户评论。

        Args:
            comment_content (str): 用户评论内容 (文本字符串)。
            deal_id (int): Deal ID，指示用户评论所属的 Deal。

        Returns:
            str: AI 生成的回复内容
        """
        pass


class ReviewArticleGenerationAgent(ContentGenerationAgent):
    """负责生成评测文章的Agent"""
    
    def get_description(self):
        return "负责生成评测文章的内容生成专员Agent"
    
    def generate_content(self, *args, **kwargs):
        """实现抽象方法，生成评测文章内容"""
        if "article_outline" in kwargs and "product_info" in kwargs:
            return self.generate_review_article_content(
                kwargs["article_outline"], 
                kwargs["product_info"]
            )
        elif "product_info" in kwargs:
            return self.generate_review_article_outline(kwargs["product_info"])
        else:
            raise ValueError("Missing required parameters for article generation")
    
    def generate_review_article_outline(self, product_info: dict):
        """
        AI 生成评测文章大纲 (Outline)。

        Args:
            product_info (dict): 商品信息，用于确定评测文章的主题和方向。

        Returns:
            str: AI 生成的评测文章大纲 (文本字符串)。
        """
        pass

    def generate_review_article_content(self, article_outline: str, product_info: dict):
        """
        AI 生成评测文章内容。

        Args:
            article_outline (str): 评测文章大纲，作为内容生成的结构化指导。
            product_info (dict): 商品信息，为文章内容提供事实依据和细节信息。

        Returns:
            str: AI 生成的评测文章内容 (文本字符串)。
        """
        pass


def create_content_generation_agent(agent_type="deal", llm_config=None):
    """
    工厂函数，用于创建不同类型的内容生成专员 Agent 实例。

    Args:
        agent_type (str): Agent类型，可选值为"deal"、"comment"或"article"
        llm_config (dict, optional): LLM 配置字典，传递给 Agent 构造函数。

    Returns:
        ContentGenerationAgent: 创建的内容生成专员 Agent 实例。
    """
    if agent_type == "deal":
        return DealGenerationAgent(llm_config=llm_config)
    elif agent_type == "comment":
        return CommentGenerationAgent(llm_config=llm_config)
    elif agent_type == "article":
        return ReviewArticleGenerationAgent(llm_config=llm_config)
    else:
        raise ValueError(f"Unknown agent type: {agent_type}")

if __name__ == '__main__':
    # (可选) 内容生成专员 Agent 的单元测试或示例代码可以放在这里
    pass