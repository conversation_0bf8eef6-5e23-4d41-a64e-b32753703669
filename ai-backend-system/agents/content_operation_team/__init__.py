"""
内容运营团队 Agent 包的初始化模块。

该模块导出内容运营团队的所有 Agent 类，包括：
- 运营经理 Agent (OperationalManagerAgent)
- 内容生成专员 Agent (ContentGenerationAgent)
- 内容评审专员 Agent (ContentReviewAgent)
- 内容收集助手 Agent (ContentCollectionAssistantAgent)
- SEO 编辑专员 Agent (SEOEditorAgent)
"""

from .operational_manager_agent import OperationalManagerAgent, create_operational_manager_agent
from .content_generation_agent import (
    ContentGenerationAgent, 
    DealGenerationAgent, 
    CommentGenerationAgent, 
    ReviewArticleGenerationAgent,
    create_content_generation_agent
)
from .content_review_agent import ContentReviewAgent, create_content_review_agent
from .content_collection_assistant_agent import (
    ContentCollectionAssistantAgent,
    DealCollectorAgent,
    ProductInfoCollectorAgent,
    CommentCollectorAgent,
    ArticleCollectorAgent,
    VideoCollectorAgent,
    create_content_collection_assistant
)
# from .seo_editor_agent import SEOEditorAgent, create_seo_editor_agent  # 待实现

__all__ = [
    # 运营经理 Agent
    "OperationalManagerAgent",
    "create_operational_manager_agent",
    
    # 内容生成专员 Agent
    "ContentGenerationAgent",
    "DealGenerationAgent",
    "CommentGenerationAgent",
    "ReviewArticleGenerationAgent",
    "create_content_generation_agent",
    
    # 内容评审专员 Agent
    "ContentReviewAgent",
    "create_content_review_agent",
    
    # 内容收集助手 Agent
    "ContentCollectionAssistantAgent",
    "DealCollectorAgent",
    "ProductInfoCollectorAgent",
    "CommentCollectorAgent",
    "ArticleCollectorAgent",
    "VideoCollectorAgent",
    "create_content_collection_assistant",
    
    # SEO 编辑专员 Agent
    # "SEOEditorAgent",
    # "create_seo_editor_agent",
]
