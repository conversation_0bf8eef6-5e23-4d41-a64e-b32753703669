"""
CrawlerEngineerAgent - 爬虫工程师代理

该代理负责:
1. 基于Crawl4AI的WebCrawler进行数据抓取
2. 基于Crawl4AI的Schema进行数据解析
3. 维护和更新指定URL的Schema
4. 响应其他Agent的数据抓取请求
"""

import logging
import json
import os
import asyncio
from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from config import get_config
from data_models import ContentType, ContentDataModel
from enum import Enum
from typing import Optional, Dict, Any, List
from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
from crawl4ai.async_configs import LLMConfig
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
from urllib.parse import urlparse, quote


class ParseType(Enum):
    ITEM_LIST = "item_list"
    ITEM_DETAIL = "item_detail"
        
class CrawlerEngineerAgent(AssistantAgent):
    """爬虫工程师代理，负责爬虫开发和数据抓取"""
    
    def __init__(
        self,
        name: str = "crawler_engineer",
        system_message: str = """You are a crawler engineer specialized in web scraping and data extraction.
Your main responsibilities include:
* 直接调用给定tool进行爬取，只要其成功返回你就认为任务完成.
Reply with TERMINATE when the task has been completed.""",
        model_client: Optional[OpenAIChatCompletionClient] = None,
        tools: Optional[List[Any]] = None,
    ) -> None:
        """初始化爬虫工程师代理
        
        Args:
            name: 代理名称
            system_message: 系统提示消息
            model_client: 模型客户端，默认使用 Gemini
            tools: 工具列表
        """
        if model_client is None:
            model_client = OpenAIChatCompletionClient(
                model=get_config()['agents']['crawler_engineer']['agent_model_name'],
                api_key=get_config()['agents']['crawler_engineer']['agent_model_api_key'],
                base_url="https://generativelanguage.googleapis.com/v1beta/openai/",  # Gemini API base URL
                model_info={
                    "vision": False,
                    "function_calling": True,
                    "json_output": True,
                    "structured_output": True,
                    "family": "unknown",
                }
            )

        # 定义爬虫相关工具
        if tools is None:
            tools = [
                self._crawl_and_parsing_result,
            ]

        super().__init__(
            name=name,
            system_message=system_message,
            model_client=model_client,
            tools=tools,
            reflect_on_tool_use=False  # Disabled to avoid Gemini API issues with tool reflection
        )

    async def _crawl_and_parsing_result(self, target_url: str, content_type: ContentType, parse_type: ParseType) -> str:
        """爬取指定URL的数据并解析结果, 首先检测是否存在schema（通过_get_schema_path获取）, 如果存在则直接返回schema并调用_crawl_url进行爬取和解析,
        否则调用_generate_schema_by_type生成schema并保存到本地，再次调用_crawl_url进行爬取和解析并返回"""
        schema_path = self._get_schema_path(target_url, content_type, parse_type)

        if os.path.exists(schema_path):
            logging.info(f"Using existing schema: {schema_path}")
            return await self._crawl_url(target_url, schema_path=schema_path, result_type="extracted_json")
        else:
            logging.info(f"Generating new schema for {target_url}")
            html_content = await self._crawl_url(target_url, result_type="raw_html")
            schema_path = await self._generate_schema_by_type(target_url, html_content, content_type, parse_type)

            if schema_path is None:
                logging.error(f"Failed to generate schema for {target_url}")
                return "[]"  # 返回空结果而不是抛出异常

            return await self._crawl_url(target_url, schema_path=schema_path, result_type="extracted_json")

            
    async def _generate_schema_by_type(self, target_url: str, html_content: str, content_type: ContentType, parse_type: ParseType) -> str:
        """基于Craw4AI的Schema生成器（JsonCssExtractionStrategy）根据HTML内容生成Schema，并根据url和content_type保存到本地"""

        # 获取对应的数据模型类
        model_class = ContentDataModel.get_model_class(content_type)

        # 创建一个实例来调用实例方法
        model_instance = model_class()

        try:
            query_str = """You are an expert in generating JSON schemas for web scraping using CSS selectors. Your task is to create a schema that can be used to extract specific information related to deals from a web page.

CRITICAL REQUIREMENTS:
1. Use ONLY CSS selectors (NOT XPath selectors)
2. CSS selectors should NOT start with "//" or contain XPath syntax like "@class" or "contains()"
3. Use proper CSS syntax like "div.class-name", ".class-name", "#id-name", "tag[attribute='value']"
4. NEVER use a single dot "." as a selector - this is invalid
5. NEVER use empty selectors or null selectors
6. For nested fields, either omit the "selector" property entirely or use a valid CSS selector
7. Ensure all selectors are valid CSS selectors that work with document.querySelector()

EXAMPLES OF CORRECT CSS SELECTORS:
- "div.node-ozbdeal" (NOT "//div[contains(@class, 'node-ozbdeal')]")
- ".product-card" (NOT "//div[@class='product-card']")
- "h2.title" (NOT "//h2[@class='title']")
- "span[data-price]" (NOT "//span[@data-price]")

INVALID SELECTORS TO AVOID:
- "." (single dot - NEVER use this)
- "" (empty string)
- null (null value)

For nested fields that don't need a specific selector, simply omit the "selector" property.

The information we want to extract includes the following fields:""" + model_instance.export_prompts()

            # Fix LLM provider name for Google AI Studio (not Vertex AI)
            provider_name = get_config()['agents']['crawler_engineer']['extractor_model_name']
            # Convert to correct format for LiteLLM
            if provider_name.startswith('gemini'):
                provider_name = f"gemini/{provider_name}"

            logging.info(f"Generating schema for {target_url} using LLM: {provider_name}")
            css_schema = JsonCssExtractionStrategy.generate_schema(
                html_content,
                schema_type="css",
                llm_config=LLMConfig(
                    provider=provider_name,
                    api_token=get_config()['agents']['crawler_engineer']['extractor_model_api_key'],
                ),
                query=query_str,
            )

            # 错误处理并logging
            if css_schema is None:
                logging.error(f"Failed to generate schema for {target_url}")
                return None

            # Validate and clean the schema before saving
            if isinstance(css_schema, str):
                css_schema = json.loads(css_schema)

            cleaned_schema = self._clean_schema(css_schema)

            schema_path = self._get_schema_path(target_url, content_type, parse_type)
            with open(schema_path, 'w', encoding='utf-8') as f:
                json.dump(cleaned_schema, f, indent=2, ensure_ascii=False)

            logging.info(f"Successfully generated, cleaned and saved schema: {schema_path}")
            return schema_path

        except Exception as e:
            logging.error(f"Failed to generate schema for {target_url}: {e}")
            return None

    def _clean_schema(self, schema: dict) -> dict:
        """Clean the schema by removing or fixing invalid CSS selectors

        Args:
            schema: The schema dictionary to clean

        Returns:
            dict: Cleaned schema with valid CSS selectors
        """
        def clean_field(field):
            """Clean a single field in the schema"""
            if isinstance(field, dict):
                # Fix invalid single dot selector
                if field.get('selector') == '.':
                    # For nested fields or list items, remove the selector entirely
                    # This tells the extraction engine to use the current context
                    if 'selector' in field:
                        del field['selector']
                        logging.warning(f"Removed invalid '.' selector from field: {field.get('name', 'unknown')}")

                # Recursively clean nested fields
                if 'fields' in field and isinstance(field['fields'], list):
                    field['fields'] = [clean_field(f) for f in field['fields']]

            return field

        # Clean the schema
        cleaned_schema = schema.copy()

        # Clean baseFields if present
        if 'baseFields' in cleaned_schema and isinstance(cleaned_schema['baseFields'], list):
            cleaned_schema['baseFields'] = [clean_field(f) for f in cleaned_schema['baseFields']]

        # Clean main fields
        if 'fields' in cleaned_schema and isinstance(cleaned_schema['fields'], list):
            cleaned_schema['fields'] = [clean_field(f) for f in cleaned_schema['fields']]

        return cleaned_schema

    async def _crawl_url(self, url: str, use_proxy: bool = True, schema_path: str = None, result_type: str = "raw_html") -> Dict[str, Any]:
        """爬取指定URL的数据
        
        Args:
            url: 目标URL
            use_proxy: 是否允许使用代理重试
            schema_path: 可选的schema文件路径
            result_type: 可选的返回结果类型，可以是"raw_html"或"extracted_json"
            
        Returns:
            Dict[str, Any]: 爬取的数据
        """
        browser_cfg = BrowserConfig(
            verbose=True,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
            }
        )
        css_schema = None
        if schema_path is not None:
            with open(schema_path, 'r', encoding='utf-8') as f:
                css_schema = json.load(f)
        strategy = JsonCssExtractionStrategy(schema=css_schema, verbose=True) if css_schema else None
        crawl_config = CrawlerRunConfig(
            extraction_strategy=strategy,
            word_count_threshold=5,
            wait_for_images=True,
            exclude_external_links=False,
            remove_overlay_elements=True,
            scan_full_page=True,
            scroll_delay=0.5,
            cache_mode=CacheMode.DISABLED,
            page_timeout=30000,
            wait_until='commit'
        )

        # 根据use_proxy参数决定使用直接爬取还是代理爬取
        target_url = url
        if use_proxy:
            config = get_config()
            # URL encode the target URL to handle query parameters properly
            encoded_url = quote(url, safe='')
            target_url = f"http://api.scrape.do?token={config['api_keys']['scrape_do_api_key']}&url={encoded_url}"
            logging.info(f"Using proxy for URL: {url} (encoded: {encoded_url})")
        
        try:
            logging.info(f"Crawling URL: {url}" + (" (with proxy)" if use_proxy else ""))
            async with AsyncWebCrawler(config=browser_cfg) as crawler:
                result = await crawler.arun(target_url, extraction_strategy=strategy, config=crawl_config)

                if not result.success:
                    raise Exception(f"Crawl failed for URL {url}: {result.error_message}")
                
                logging.info(f"Crawl successful for URL: {url}")
                
                if result_type == "raw_html":
                    return result.html
                elif result_type == "extracted_json":
                    return result.extracted_content
                else:
                    raise ValueError(f"Invalid result_type: {result_type}")
        except Exception as e:
            logging.error(f"Crawl failed for URL {url}: {str(e)}")
            raise



    def _get_schema_path(self, target_url: str, content_type: ContentType, parse_type: ParseType) -> str:
        """获取Schema文件路径
        
        Args:
            target_url: 目标URL
            content_type: 内容类型
            parse_type: 解析类型
        Returns:
            str: Schema文件的完整路径
        """
        parsed_url = urlparse(target_url)
        domain = parsed_url.netloc
        path = parsed_url.path.strip('/')
        
        # 创建schema目录
        schema_dir = os.path.join('ai-backend-system/', 'schemas', domain)
        os.makedirs(schema_dir, exist_ok=True)
        
        if parse_type == ParseType.ITEM_LIST:
            # 处理列表页面，提取主路径作为文件名前缀
            path_prefix = path.split('/')[0] if path else parsed_url.query.split('&')[0].split('=')[0]
            filename = f"{path_prefix}_{content_type.value}.json"
        else:  # ParseType.ITEM_DETAIL
            # 处理详情页面，提取最后一层路径的父级作为文件名前缀
            path_parts = path.split('/')
            if len(path_parts) >= 2:
                path_prefix = path_parts[-2]
            else:
                path_prefix = path_parts[0]
            filename = f"{path_prefix}_{content_type.value}.json"
            
        return os.path.join(schema_dir, filename)

# 测试爬虫工程师代理 - 使用 Agent as Tool 方式
if __name__ == "__main__":
    from autogen_agentchat.tools import AgentTool
    from autogen_agentchat.ui import Console
    import asyncio
    from data_models import ContentType

    class CrawlerOrchestratorAgent(AssistantAgent):
        """爬虫编排代理，负责管理和协调爬虫任务"""

        def __init__(
            self,
            name: str = "crawler_orchestrator",
            system_message: str = """You are a crawler orchestrator responsible for managing web crawling tasks.
Your main responsibilities include:
* Coordinating with crawler engineer agents to execute crawling tasks
* Managing pagination logic and determining next page URLs
* Monitoring crawling progress and applying business rules (like cutoff times)
* Terminating tasks when completion criteria are met

When working with crawler agents:
1. Use the crawler tool to crawl specific URLs with appropriate content types and parse types
2. For pagination tasks, determine the next page URL based on the current URL pattern
3. For time-sensitive tasks, check if any content is older than the cutoff time and terminate if needed
4. Reply with TERMINATE when the task has been completed successfully

Always be efficient and follow the given constraints (max pages, cutoff times, etc.).""",
            model_client: Optional[OpenAIChatCompletionClient] = None,
            tools: Optional[List[Any]] = None,
        ) -> None:
            if model_client is None:
                model_client = OpenAIChatCompletionClient(
                    model=get_config()['agents']['crawler_engineer']['agent_model_name'],
                    api_key=get_config()['agents']['crawler_engineer']['agent_model_api_key'],
                    base_url="https://generativelanguage.googleapis.com/v1beta/openai/",
                    model_info={
                        "vision": False,
                        "function_calling": True,
                        "json_output": True,
                        "structured_output": True,
                        "family": "unknown",
                    }
                )

            super().__init__(
                name=name,
                system_message=system_message,
                model_client=model_client,
                tools=tools or [],
                reflect_on_tool_use=False
            )

    async def test_agent_as_tool_approach():
        """测试使用 Agent as Tool 方式的爬虫工作流"""

        # 创建爬虫工程师代理
        crawler_engineer = CrawlerEngineerAgent()

        # 将爬虫工程师代理转换为工具
        crawler_tool = AgentTool(agent=crawler_engineer)

        # 创建编排代理，使用爬虫工具
        orchestrator = CrawlerOrchestratorAgent(
            tools=[crawler_tool]
        )

        # 测试用例1: OzBargain
        print("Testing OzBargain crawler with Agent as Tool approach...")
        try:
            await Console(orchestrator.run_stream(
                task=f"""Please crawl and parse deals from OzBargain using this URL.
                You should determine the paging rules according to the URL and crawl pages until reaching the given max page number.
                **Crucially, if you find even a single deal that is older than the cutoff time, you MUST immediately terminate the task.**

                Start page URL: https://www.ozbargain.com.au/deals?page=0
                Cutoff time (terminate task if any deal on the crawled page is older than this time): 08/03/2025 - 23:06
                Max paging number (The maximum number of pages to crawl in total): 1
                Use content type: {ContentType.DEAL.value}
                Use parse type: {ParseType.ITEM_LIST.value}

                Use the crawler tool to execute the crawling task. Reply with TERMINATE when completed."""
            ))
        except Exception as e:
            print(f"OzBargain test failed: {str(e)}")

        print("\n" + "=" * 60)

        # 测试用例2: CamelCamelCamel
        print("Testing CamelCamelCamel crawler with Agent as Tool approach...")
        try:
            await Console(orchestrator.run_stream(
                task=f"""Please crawl and parse the deals from CamelCamelCamel using this URL.
                You should determine the paging rules according to the URL and crawl pages until reaching the given max page number.

                Start page URL: https://au.camelcamelcamel.com/popular?p=1
                Use content type: {ContentType.DEAL.value}
                Use parse type: {ParseType.ITEM_LIST.value}
                Max page number: 1

                Use the crawler tool to execute the crawling task. Reply with TERMINATE when completed."""
            ))
        except Exception as e:
            print(f"CamelCamelCamel test failed: {str(e)}")

        print("\n" + "=" * 60)

        # 测试用例3: CamelCamelCamel Deal Detail
        print("Testing CamelCamelCamel deal detail crawler with Agent as Tool approach...")
        try:
            await Console(orchestrator.run_stream(
                task=f"""Please crawl and parse the deal detail from CamelCamelCamel using this URL.
                This is a product detail page that contains price history and deal information.

                Target URL: https://au.camelcamelcamel.com/product/B07741S7XP?active=price_amazon&context=popular
                Use content type: {ContentType.DEAL.value}
                Use parse type: {ParseType.ITEM_DETAIL.value}

                Use the crawler tool to execute the crawling task. Reply with TERMINATE when completed."""
            ))
        except Exception as e:
            print(f"CamelCamelCamel deal detail test failed: {str(e)}")

    # 运行测试
    print("=" * 60)
    print("CRAWLER ENGINEER AGENT TESTS - AGENT AS TOOL APPROACH")
    print("=" * 60)

    # 运行使用 Agent as Tool 方式的测试
    asyncio.run(test_agent_as_tool_approach())