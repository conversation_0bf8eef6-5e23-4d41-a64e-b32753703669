from typing import Dict, Optional, ClassVar, List
from pydantic import Field, validator
from datetime import datetime
from decimal import Decimal
from enum import Enum
from data_models.content_data_model import ContentDataModel
from data_models.content_type import ContentType


class ProductCategory(str, Enum):
    """商品主分类枚举"""
    ELECTRONICS = "electronics"
    FASHION = "fashion"
    HOME = "home"
    BEAUTY = "beauty"
    SPORTS = "sports"
    TOYS = "toys"
    BOOKS = "books"
    GROCERY = "grocery"
    AUTOMOTIVE = "automotive"
    HEALTH = "health"
    OTHER = "other"
    
class Region(str, Enum):
    """支持的国家/地区枚举"""
    # 大洋洲
    AU = "au"  # 澳大利亚
    NZ = "nz"  # 新西兰
    
    # 北美洲
    US = "us"  # 美国
    CA = "ca"  # 加拿大
    
    # 欧洲
    UK = "uk"  # 英国
    DE = "de"  # 德国
    FR = "fr"  # 法国
    IT = "it"  # 意大利
    ES = "es"  # 西班牙
    NL = "nl"  # 荷兰
    SE = "se"  # 瑞典
    
    # 亚洲
    JP = "jp"  # 日本
    KR = "kr"  # 韩国
    SG = "sg"  # 新加坡
    MY = "my"  # 马来西亚
    TH = "th"  # 泰国
    VN = "vn"  # 越南
    ID = "id"  # 印度尼西亚
    PH = "ph"  # 菲律宾
    IN = "in"  # 印度
    
    # 中东
    AE = "ae"  # 阿联酋
    SA = "sa"  # 沙特阿拉伯
    TR = "tr"  # 土耳其
    
    # 南美洲
    BR = "br"  # 巴西
    MX = "mx"  # 墨西哥
    AR = "ar"  # 阿根廷
    CL = "cl"  # 智利

    @classmethod
    def get_currency(cls, region: str) -> str:
        """获取区域对应的货币代码"""
        currency_mapping = {
            cls.AU.value: "AUD",  # 澳大利亚元
            cls.NZ.value: "NZD",  # 新西兰元
            cls.US.value: "USD",  # 美元
            cls.CA.value: "CAD",  # 加拿大元
            cls.UK.value: "GBP",  # 英镑
            cls.DE.value: "EUR",  # 欧元
            cls.FR.value: "EUR",  # 欧元
            cls.IT.value: "EUR",  # 欧元
            cls.ES.value: "EUR",  # 欧元
            cls.NL.value: "EUR",  # 欧元
            cls.SE.value: "SEK",  # 瑞典克朗
            cls.JP.value: "JPY",  # 日元
            cls.KR.value: "KRW",  # 韩元
            cls.SG.value: "SGD",  # 新加坡元
            cls.MY.value: "MYR",  # 马来西亚林吉特
            cls.TH.value: "THB",  # 泰铢
            cls.VN.value: "VND",  # 越南盾
            cls.ID.value: "IDR",  # 印尼盾
            cls.PH.value: "PHP",  # 菲律宾比索
            cls.IN.value: "INR",  # 印度卢比
            cls.AE.value: "AED",  # 阿联酋迪拉姆
            cls.SA.value: "SAR",  # 沙特里亚尔
            cls.TR.value: "TRY",  # 土耳其里拉
            cls.BR.value: "BRL",  # 巴西雷亚尔
            cls.MX.value: "MXN",  # 墨西哥比索
            cls.AR.value: "ARS",  # 阿根廷比索
            cls.CL.value: "CLP"   # 智利比索
        }
        return currency_mapping.get(region, "USD")  # 默认返回美元

    @classmethod
    def get_language(cls, region: str) -> str:
        """获取区域对应的主要语言代码"""
        language_mapping = {
            cls.AU.value: "en",  # 英语
            cls.NZ.value: "en",  # 英语
            cls.US.value: "en",  # 英语
            cls.CA.value: "en",  # 英语（也可能是法语）
            cls.UK.value: "en",  # 英语
            cls.DE.value: "de",  # 德语
            cls.FR.value: "fr",  # 法语
            cls.IT.value: "it",  # 意大利语
            cls.ES.value: "es",  # 西班牙语
            cls.NL.value: "nl",  # 荷兰语
            cls.SE.value: "sv",  # 瑞典语
            cls.JP.value: "ja",  # 日语
            cls.KR.value: "ko",  # 韩语
            cls.SG.value: "en",  # 英语
            cls.MY.value: "ms",  # 马来语
            cls.TH.value: "th",  # 泰语
            cls.VN.value: "vi",  # 越南语
            cls.ID.value: "id",  # 印尼语
            cls.PH.value: "en",  # 英语
            cls.IN.value: "en",  # 英语（印地语等）
            cls.AE.value: "ar",  # 阿拉伯语
            cls.SA.value: "ar",  # 阿拉伯语
            cls.TR.value: "tr",  # 土耳其语
            cls.BR.value: "pt",  # 葡萄牙语
            cls.MX.value: "es",  # 西班牙语
            cls.AR.value: "es",  # 西班牙语
            cls.CL.value: "es"   # 西班牙语
        }
        return language_mapping.get(region, "en")  # 默认返回英语

    @classmethod
    def get_timezone(cls, region: str) -> str:
        """获取区域对应的主要时区"""
        timezone_mapping = {
            cls.AU.value: "Australia/Sydney",    # 澳大利亚东部时间
            cls.NZ.value: "Pacific/Auckland",    # 新西兰时间
            cls.US.value: "America/New_York",    # 美国东部时间
            cls.CA.value: "America/Toronto",     # 加拿大东部时间
            cls.UK.value: "Europe/London",       # 英国时间
            cls.DE.value: "Europe/Berlin",       # 德国时间
            cls.FR.value: "Europe/Paris",        # 法国时间
            cls.IT.value: "Europe/Rome",         # 意大利时间
            cls.ES.value: "Europe/Madrid",       # 西班牙时间
            cls.NL.value: "Europe/Amsterdam",    # 荷兰时间
            cls.SE.value: "Europe/Stockholm",    # 瑞典时间
            cls.JP.value: "Asia/Tokyo",          # 日本时间
            cls.KR.value: "Asia/Seoul",          # 韩国时间
            cls.SG.value: "Asia/Singapore",      # 新加坡时间
            cls.MY.value: "Asia/Kuala_Lumpur",   # 马来西亚时间
            cls.TH.value: "Asia/Bangkok",        # 泰国时间
            cls.VN.value: "Asia/Ho_Chi_Minh",    # 越南时间
            cls.ID.value: "Asia/Jakarta",        # 印度尼西亚时间
            cls.PH.value: "Asia/Manila",         # 菲律宾时间
            cls.IN.value: "Asia/Kolkata",        # 印度时间
            cls.AE.value: "Asia/Dubai",          # 阿联酋时间
            cls.SA.value: "Asia/Riyadh",         # 沙特阿拉伯时间
            cls.TR.value: "Europe/Istanbul",     # 土耳其时间
            cls.BR.value: "America/Sao_Paulo",   # 巴西时间
            cls.MX.value: "America/Mexico_City", # 墨西哥时间
            cls.AR.value: "America/Buenos_Aires",# 阿根廷时间
            cls.CL.value: "America/Santiago"     # 智利时间
        }
        return timezone_mapping.get(region, "UTC")  # 默认返回 UTC

class RegionPriceInfo(ContentDataModel):
    """区域价格信息模型"""
    price_normal_180days: Optional[Decimal] = Field(None, description="180 天常规价格")
    price_lowest: Optional[Decimal] = Field(None, description="历史最低价格")
    price_average: Optional[Decimal] = Field(None, description="平均价格")
    currency: str = Field(..., description="货币代码", example="AUD")

    @validator('price_lowest')
    def validate_price_lowest(cls, v, values):
        """验证最低价格不高于常规价格"""
        if v is not None and values.get('price_normal_180days') is not None:
            if v > values['price_normal_180days']:
                raise ValueError("Lowest price cannot be higher than normal price")
        return v

class ProductData(ContentDataModel):
    """
    ProductData model, based on Pydantic BaseModel.
    """
    content_type: ClassVar[ContentType] = ContentType.PRODUCT

    product_name: str = Field(..., max_length=255, description="商品名称")
    brand: Optional[str] = Field(None, max_length=100, description="品牌")
    description: Optional[str] = Field(None, description="商品描述")
    category: ProductCategory = Field(..., description="商品主分类")
    sub_category: Optional[str] = Field(None, max_length=100, description="商品子分类")
    asin: Optional[str] = Field(None, max_length=50, description="Amazon 识别号")
    image_url: Optional[str] = Field(None, max_length=255, description="商品主图 URL")
    created_at: Optional[datetime] = Field(default_factory=datetime.now, description="创建时间")
    tags: List[str] = Field(default_factory=list, description="商品标签")
    
    # 新增：区域价格信息字典
    region_prices: Dict[Region, RegionPriceInfo] = Field(
        default_factory=dict,
        description="各区域的价格信息"
    )

    @validator('sub_category')
    def validate_sub_category(cls, v, values):
        """验证子分类是否符合主分类的规则"""
        if not v:
            return v
            
        # 定义每个主分类允许的子分类
        valid_sub_categories = {
            ProductCategory.ELECTRONICS: [
                "smartphones", "laptops", "audio", "cameras", "gaming",
                "wearables", "accessories"
            ],
            # ... 其他分类定义保持不变 ...
        }
        
        category = values.get('category')
        if category and category in valid_sub_categories:
            if v.lower() not in valid_sub_categories[category]:
                raise ValueError(
                    f"Invalid sub_category '{v}' for category '{category}'. "
                    f"Valid options are: {valid_sub_categories[category]}"
                )
        return v

    @validator('tags')
    def validate_tags(cls, v):
        """验证标签列表"""
        if len(v) > 10:
            raise ValueError("Maximum 10 tags allowed")
        return [tag.lower() for tag in v]

    class Config:
        """Pydantic 模型配置"""
        orm_mode = True
        schema_extra = {
            "example": {
                "product_name": "Bose QuietComfort 45",
                "brand": "Bose",
                "description": "舒适的降噪耳机",
                "category": "electronics",
                "sub_category": "audio",
                "asin": "B098FKXT8L",
                "image_url": "https://example.com/images/qc45.jpg",
                "tags": ["noise-cancelling", "wireless", "headphones", "premium"],
                "region_prices": {
                    "au": {
                        "price_normal_180days": 329.00,
                        "price_lowest": 279.00,
                        "price_average": 300.00,
                        "currency": "AUD"
                    },
                    "us": {
                        "price_normal_180days": 249.00,
                        "price_lowest": 199.00,
                        "price_average": 220.00,
                        "currency": "USD"
                    }
                }
            }
        }

    def export_prompts(self) -> str:
        """
        Export prompts for LLM to understand the data model betterly.
        """
        return """The information we want to define includes the following fields:
        - product_name: The name of the product
        - brand: The brand of the product
        - description: The description of the product
        - category: The main category of the product (e.g., electronics, fashion)
        - sub_category: The sub-category of the product (e.g., audio, smartphones)
        - asin: The Amazon ID of the product
        - image_url: The URL of the product image
        - created_at: The date and time when the product was created
        - tags: List of relevant tags for the product
        - region_prices: A dictionary of regional price information, where:
            - key: region code (e.g., 'au' for Australia, 'us' for United States)
            - value: price information object containing:
                - price_normal_180days: The normal price in the last 180 days
                - price_lowest: The lowest price in history
                - price_average: The average price in history
                - currency: The currency code (e.g., 'AUD', 'USD')
        
        Example of region_prices:
        {
            "au": {
                "price_normal_180days": 329.00,
                "price_lowest": 279.00,
                "price_average": 300.00,
                "currency": "AUD"
            },
            "us": {
                "price_normal_180days": 249.00,
                "price_lowest": 199.00,
                "price_average": 220.00,
                "currency": "USD"
            }
        }
        """