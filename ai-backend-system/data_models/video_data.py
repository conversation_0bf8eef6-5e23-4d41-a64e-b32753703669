"""
定义视频数据模型 (VideoData) 类。

该模块使用 Pydantic 库定义视频数据的结构和验证规则。
包含视频的基本信息，如标题、URL、时长、发布者等。
"""

from pydantic import BaseModel, Field, HttpUrl, validator
from datetime import datetime, timedelta
from typing import Optional, List, ClassVar, Dict, Any
from data_models.content_data_model import ContentDataModel
from data_models.content_type import ContentType
from data_models.base_functions import validate_region, get_region_list

class VideoData(ContentDataModel):
    """视频数据模型，包含视频的完整信息"""
    
    # 基本类型标识
    content_type: ClassVar[ContentType] = ContentType.VIDEO
    
    # 视频基本信息
    title: Optional[str] = Field(None, description="视频标题")
    url: Optional[HttpUrl] = Field(None, description="视频URL")
    thumbnail: Optional[HttpUrl] = Field(None, description="视频缩略图URL")
    
    # 视频详细信息
    description: Optional[str] = Field(None, description="视频描述")
    duration: Optional[timedelta] = Field(None, description="视频时长")
    publish_date: Optional[datetime] = Field(None, description="发布日期")
    
    # 创作者信息
    channel_name: Optional[str] = Field(None, description="频道/创作者名称")
    channel_url: Optional[HttpUrl] = Field(None, description="频道/创作者URL")
    channel_thumbnail: Optional[HttpUrl] = Field(None, description="频道/创作者缩略图")
    
    # 分类和标签
    categories: Optional[List[str]] = Field(None, description="视频分类")
    tags: Optional[List[str]] = Field(None, description="视频标签")
    
    # 平台信息
    platform: Optional[str] = Field(None, description="视频平台(如YouTube, TikTok)")
    
    # 区域信息
    region: Optional[str] = Field(None, description="区域代码，如 'au', 'us'")
    
    # 额外信息
    metadata: Optional[Dict[str, Any]] = Field(None, description="其他元数据")
    
    @validator('region')
    def validate_region(cls, v):
        """验证区域代码是否在配置的区域列表中"""
        if v is not None:
            validate_region(v)
        return v
    
    def export_prompts(self) -> str:
        """导出视频数据模型的提示"""
        return """The video data model includes the following fields:
        - title: The title of the video
        - url: The URL of the video
        - thumbnail: URL to the video thumbnail image
        - description: A description of the video content
        - duration: The length of the video
        - publish_date: When the video was published
        - channel_name: Name of the channel or creator
        - channel_url: URL to the channel or creator's page
        - channel_thumbnail: URL to the channel or creator's thumbnail image
        - categories: Categories the video belongs to
        - tags: Tags associated with the video
        - platform: The video platform (e.g., YouTube, TikTok)
        - region: Region code (e.g., 'au', 'us')
        - metadata: Additional metadata about the video"""

    class Config:
        """Pydantic 模型配置"""
        orm_mode = True
        schema_extra = {
            "example": {
                "title": "10 Amazing Tech Gadgets for 2024",
                "url": "https://www.youtube.com/watch?v=example123",
                "thumbnail": "https://i.ytimg.com/vi/example123/maxresdefault.jpg",
                "description": "Check out these 10 incredible tech gadgets that will change your life in 2024!",
                "duration": "PT15M30S",  # ISO 8601 duration format
                "publish_date": "2024-01-15T10:00:00",
                "channel_name": "TechReviewsDaily",
                "channel_url": "https://www.youtube.com/c/techreviewsdaily",
                "channel_thumbnail": "https://yt3.ggpht.com/example",
                "categories": ["Technology", "Gadgets", "Reviews"],
                "tags": ["tech", "gadgets", "2024", "reviews"],
                "platform": "YouTube",
                "region": "us",
                "metadata": {
                    "is_sponsored": False,
                    "language": "en",
                    "quality": "4K"
                }
            }
        } 