"""
定义评论数据模型 (CommentData) 类。

该模块使用 Pydantic 库定义评论数据的结构和验证规则。
包含评论的基本信息，如评论内容、时间戳等。
"""

from pydantic import BaseModel, Field, validator
from datetime import datetime
from typing import Optional, List, ClassVar
from data_models.content_data_model import ContentDataModel
from data_models.content_type import ContentType
from data_models.base_functions import *

class CommentData(ContentDataModel):
    """评论数据模型，包含评论的完整信息"""
    
    # 基本类型标识
    content_type: ClassVar[ContentType] = ContentType.COMMENT  # 使用点号访问枚举值
    
    # 评论基本信息
    content: Optional[str] = Field(None, description="评论内容")
    timestamp: Optional[datetime] = Field(None, description="评论时间")
    
    # 区域信息
    region: Optional[str] = Field(None, description="区域代码，如 'au', 'us'")
    
    @validator('region')
    def validate_region(cls, v):
        """验证区域代码是否在配置的区域列表中"""
        if v is not None:
            validate_region(v)
        return v
        
    def export_prompts(self) -> str:
        """导出评论数据模型的 prompts"""
        return """The comment data model includes the following fields:
        - content: The content of the comment
        - timestamp: The date and time when the comment was posted
        - region: Region code (e.g., 'au', 'us')"""

    class Config:
        """Pydantic 模型配置"""
        orm_mode = True
        schema_extra = {
            "example": {
                "content": "None of the car companies leadership currently has demonstrated...",
                "timestamp": "2025-03-06T17:05:00",
                "region": "us"
            }
        }

def create_comment_data(**data) -> CommentData:
    """
    工厂函数，用于创建 CommentData 实例
    
    Args:
        **data: CommentData 模型的字段数据
        
    Returns:
        CommentData: 创建的评论数据实例
    """
    return CommentData(**data) 