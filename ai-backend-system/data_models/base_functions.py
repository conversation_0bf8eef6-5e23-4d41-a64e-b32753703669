"""
数据模型的公共函数和工具。

该模块提供了所有数据模型共享的工具函数，减少代码重复。
"""

from typing import List, Type, TypeVar, Dict, Any
from config import get_config
from pydantic import BaseModel

T = TypeVar('T', bound=BaseModel)

def get_region_list() -> List[str]:
    """
    从配置文件获取支持的区域列表
    
    Returns:
        List[str]: 支持的区域代码列表
    """
    config = get_config()
    regions = config.get('regions', [])
    if not regions:
        # 默认区域列表，仅在配置文件中未定义时使用
        return ['us', 'uk', 'au', 'ca', 'de', 'fr', 'jp']
    return regions

def create_model_instance(model_class: Type[T], **data) -> T:
    """
    通用工厂函数，用于创建数据模型实例
    
    Args:
        model_class: 数据模型类
        **data: 模型的字段数据
        
    Returns:
        创建的数据模型实例
    """
    return model_class(**data)

def validate_region(region: str) -> bool:
    """
    验证区域代码是否在配置的区域列表中
    
    Args:
        region: 区域代码
        
    Returns:
        bool: 区域代码是否有效
        
    Raises:
        ValueError: 当区域代码无效时
    """
    if region is None:
        return True
        
    regions = get_region_list()
    if region.lower() not in [r.lower() for r in regions]:
        raise ValueError(f"Invalid region: {region}. Must be one of: {', '.join(regions)}")
    return True

def get_model_example(model_class: Type[T]) -> Dict[str, Any]:
    """
    获取数据模型的示例数据
    
    Args:
        model_class: 数据模型类
        
    Returns:
        Dict[str, Any]: 示例数据字典
    """
    if hasattr(model_class.Config, 'schema_extra') and 'example' in model_class.Config.schema_extra:
        return model_class.Config.schema_extra['example']
    return {} 