"""
定义搜索结果数据模型 (SearchResultData) 类。

该模块使用 Pydantic 库定义搜索结果数据的结构和验证规则。
包含搜索结果的基本信息，如标题、URL、摘要等。
"""

from pydantic import BaseModel, Field, HttpUrl
from datetime import datetime
from typing import Optional, List, ClassVar, Dict, Any
from data_models.content_data_model import ContentDataModel
from data_models.content_type import ContentType

class SearchResultData(ContentDataModel):
    """搜索结果数据模型，包含搜索结果的完整信息"""
    
    # 基本类型标识
    content_type: ClassVar[ContentType] = ContentType.SEARCH_RESULT
    
    # 搜索结果基本信息
    title: Optional[str] = Field(None, description="搜索结果标题")
    url: Optional[HttpUrl] = Field(None, description="搜索结果URL")
    snippet: Optional[str] = Field(None, description="搜索结果摘要")
    
    # 元数据
    source: Optional[str] = Field(None, description="搜索引擎来源")
    position: Optional[int] = Field(None, description="搜索结果位置")
    query: Optional[str] = Field(None, description="搜索查询")
    search_time: Optional[datetime] = Field(None, description="搜索时间")
    
    # 额外信息
    thumbnail: Optional[HttpUrl] = Field(None, description="缩略图URL")
    metadata: Optional[Dict[str, Any]] = Field(None, description="其他元数据")
    
    def export_prompts(self) -> str:
        """导出搜索结果数据模型的提示"""
        return """The search result data model includes the following fields:
        - title: The title of the search result
        - url: The URL of the search result
        - snippet: A brief excerpt or description of the search result
        - source: The search engine source (e.g., Google, Bing)
        - position: The position of the result in search rankings
        - query: The search query that produced this result
        - search_time: When the search was performed
        - thumbnail: URL to a thumbnail image if available
        - metadata: Additional metadata about the search result"""

    class Config:
        """Pydantic 模型配置"""
        orm_mode = True
        schema_extra = {
            "example": {
                "title": "Best Noise Cancelling Headphones 2024",
                "url": "https://example.com/best-headphones-2024",
                "snippet": "Our comprehensive guide to the best noise cancelling headphones in 2024...",
                "source": "Google",
                "position": 3,
                "query": "best noise cancelling headphones",
                "search_time": "2024-03-15T14:30:00",
                "thumbnail": "https://example.com/images/headphones-thumb.jpg",
                "metadata": {
                    "page_type": "article",
                    "published_date": "2024-02-10",
                    "author": "Tech Reviews Team"
                }
            }
        }

def create_search_result_data(**data) -> SearchResultData:
    """
    工厂函数，用于创建 SearchResultData 实例
    
    Args:
        **data: SearchResultData 模型的字段数据
        
    Returns:
        SearchResultData: 创建的搜索结果数据实例
    """
    return SearchResultData(**data) 