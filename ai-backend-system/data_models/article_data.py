"""
定义文章数据模型 (ArticleData) 类。

该模块使用 Pydantic 库定义文章数据的结构和验证规则。
包含文章的基本信息，如标题、URL、摘要、发布日期、来源网站等。
"""

from pydantic import BaseModel, Field, HttpUrl, validator
from datetime import datetime
from typing import Optional, List, ClassVar, Dict, Any
from data_models.content_data_model import ContentDataModel
from data_models.content_type import ContentType
from data_models.base_functions import validate_region, get_region_list

class ArticleStatistics(BaseModel):
    """文章统计数据模型"""
    views: Optional[int] = Field(None, description="阅读次数")
    likes: Optional[int] = Field(None, description="点赞数")
    comments: Optional[int] = Field(None, description="评论数")
    shares: Optional[int] = Field(None, description="分享数")

class ArticleData(ContentDataModel):
    """文章数据模型，包含文章的完整信息"""
    
    # 基本类型标识
    content_type: ClassVar[ContentType] = ContentType.ARTICLE
    
    # 文章基本信息
    title: Optional[str] = Field(None, description="文章标题")
    url: Optional[HttpUrl] = Field(None, description="文章URL")
    thumbnail: Optional[HttpUrl] = Field(None, description="文章缩略图URL")
    
    # 文章详细信息
    abstract: Optional[str] = Field(None, description="文章摘要")
    content: Optional[str] = Field(None, description="文章内容")
    publish_date: Optional[datetime] = Field(None, description="发布日期")
    
    # 来源信息
    website_name: Optional[str] = Field(None, description="来源网站名称")
    website_url: Optional[HttpUrl] = Field(None, description="来源网站URL")
    author: Optional[str] = Field(None, description="作者")
    author_url: Optional[HttpUrl] = Field(None, description="作者主页URL")
    
    # 分类和标签
    categories: Optional[List[str]] = Field(None, description="文章分类")
    tags: Optional[List[str]] = Field(None, description="文章标签")
    
    # 统计数据
    statistics: Optional[ArticleStatistics] = Field(None, description="文章统计数据")
    
    # 区域信息
    region: Optional[str] = Field(None, description="区域代码，如 'au', 'us'")
    
    # 产品关联
    product_id: Optional[int] = Field(None, description="关联产品ID")
    product_name: Optional[str] = Field(None, description="关联产品名称")
    
    # 额外信息
    metadata: Optional[Dict[str, Any]] = Field(None, description="其他元数据")
    
    @validator('region')
    def validate_region(cls, v):
        """验证区域代码是否在配置的区域列表中"""
        if v is not None:
            validate_region(v)
        return v
    
    def export_prompts(self) -> str:
        """导出文章数据模型的提示"""
        return """The article data model includes the following fields:
        - title: The title of the article
        - url: The URL of the article
        - thumbnail: URL to the article thumbnail image
        - abstract: A brief summary of the article content
        - content: The full content of the article
        - publish_date: When the article was published
        - website_name: Name of the source website
        - website_url: URL of the source website
        - author: Name of the author
        - author_url: URL to the author's page
        - categories: Categories the article belongs to
        - tags: Tags associated with the article
        - statistics: Statistical data including views, likes, comments, shares
        - region: Region code (e.g., 'au', 'us')
        - product_id: ID of the related product
        - product_name: Name of the related product
        - metadata: Additional metadata about the article"""

    class Config:
        """Pydantic 模型配置"""
        orm_mode = True
        schema_extra = {
            "example": {
                "title": "10 Best Smartphones for Photography in 2024",
                "url": "https://www.techreviews.com/best-camera-phones-2024",
                "thumbnail": "https://www.techreviews.com/images/camera-phones-2024.jpg",
                "abstract": "Our comprehensive guide to the best smartphones for photography enthusiasts in 2024.",
                "content": "In this article, we review the top 10 smartphones for photography in 2024...",
                "publish_date": "2024-01-15T10:00:00",
                "website_name": "TechReviews",
                "website_url": "https://www.techreviews.com",
                "author": "Jane Smith",
                "author_url": "https://www.techreviews.com/authors/jane-smith",
                "categories": ["Smartphones", "Photography", "Reviews"],
                "tags": ["camera phones", "mobile photography", "2024", "reviews"],
                "statistics": {
                    "views": 15000,
                    "likes": 750,
                    "comments": 85,
                    "shares": 120
                },
                "region": "us",
                "product_id": 123,
                "product_name": "iPhone 15 Pro Max",
                "metadata": {
                    "is_sponsored": False,
                    "reading_time": "12 minutes",
                    "editor_pick": True
                }
            }
        } 