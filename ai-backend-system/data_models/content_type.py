"""
ContentType 枚举定义。

该模块定义了系统支持的所有内容类型。
"""

from enum import Enum

class ContentType(Enum):
    """内容类型枚举
    
    定义了系统支持的所有内容类型：
    - DEAL: 优惠信息
    - PRODUCT: 商品信息
    - COMMENT: 评论信息
    - ARTICLE: 文章信息
    - VIDEO: 视频信息
    - SEARCH_RESULT: 搜索结果
    """
    DEAL = "deal"
    PRODUCT = "product"
    COMMENT = "comment"
    ARTICLE = "article"
    VIDEO = "video" # such as youtube, tiktok, etc.
    SEARCH_RESULT = "search_result" # TODO: using search api to instead of using crawler