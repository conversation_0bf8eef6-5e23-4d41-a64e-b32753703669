# ai-backend-system/data_models/deal_data.py
"""
定义 Deal 数据模型 (DealData) 类。

该模块使用 Pydantic 库定义了 Deal 数据的结构和验证规则，确保数据的一致性和有效性。
DealData 模型包含了商品交易的完整信息，包括基本信息、价格信息、链接信息等。

数据模型字段包括：
- name: 商品名称
- date: 发布日期和时间
- img_link: 商品图片链接
- source_site: 来源网站
- current_price: 当前价格
- original_price: 原始价格
- average_price: 平均价格
- original_link: 原始商品链接
- deal_link: 当前网站的商品详情页链接
- description: 商品完整描述
- categories: 商品所属类别
- expiration: 优惠过期时间
- price_history: 价格历史记录
- region: 区域代码
"""

from data_models.content_data_model import ContentDataModel
from data_models.content_type import ContentType
from data_models.product_data import ProductCategory
from data_models.base_functions import validate_region, get_region_list
from pydantic import BaseModel, Field, HttpUrl, validator
from datetime import datetime
from decimal import Decimal
from typing import Optional, List, Dict, ClassVar

class DealData(ContentDataModel):
    """Deal 数据模型 (DealData), 基于 Pydantic BaseModel 构建"""
    content_type: ClassVar[ContentType] = ContentType.DEAL  # 使用点号访问枚举值
    
    # 基本信息
    name: Optional[str] = Field(None, description="The name of the deal")
    date: Optional[datetime] = Field(None, description="The date and time when the deal was posted")
    img_link: Optional[HttpUrl] = Field(None, description="Image link of the deal")
    source_site: Optional[str] = Field(None, description="Source website of the deal")
    
    # 价格信息
    current_price: Optional[Decimal] = Field(None, description="The current price of the deal")
    original_price: Optional[Decimal] = Field(None, description="The original price of the deal")
    average_price: Optional[Decimal] = Field(None, description="The average price of the deal")
    
    # 链接信息
    original_link: Optional[HttpUrl] = Field(None, description="Original deal link (redirect to external website)")
    deal_link: Optional[HttpUrl] = Field(None, description="Link to deal detail page of the current website")
    
    # 描述和分类
    description: Optional[str] = Field(None, description="Full description of the deal")
    categories: Optional[List[ProductCategory]] = Field(None, description="The categories to which the deal belongs")
    
    # 时间和历史记录
    expiration: Optional[datetime] = Field(None, description="The expiration date of the deal")
    
    # 区域信息
    region: Optional[str] = Field(None, description="区域代码，如 'au', 'us'")
    
    @validator('region')
    def validate_region(cls, v):
        """验证区域代码是否在配置的区域列表中"""
        if v is not None:
            validate_region(v)
        return v

    class Config:
        """Pydantic 模型配置"""
        orm_mode = True
        schema_extra = {
            "example": {
                "name": "Sony WH-1000XM4 无线降噪耳机",
                "date": "2024-02-15T10:30:00",
                "img_link": "https://example.com/images/sony-wh1000xm4.jpg",
                "source_site": "Amazon",
                "current_price": 248.00,
                "original_price": 349.99,
                "average_price": 299.99,
                "original_link": "https://amazon.com/dp/B0863TXGM3",
                "deal_link": "https://deals.example.com/sony-wh1000xm4",
                "description": "Sony WH-1000XM4 无线降噪耳机，支持多设备配对，30小时续航",
                "categories": ["ELECTRONICS", "TOYS"],
                "expiration": "2024-02-20T23:59:59",
                "region": "us"
            }
        }

    def export_prompts(self) -> str:
        """
        导出 DealData 的 prompts for LLM to understand the data model betterly.
        """
        return """The information we want to define includes the following fields:
        - name: The name of the deal
        - date: The date and time when the deal was posted
        - thumb_image_url: Thumb image link url of the deal, make sure it is a valid image link with http or https full url
        - source_site: Source website of the deal
        - current_price: The current price of the deal (optional)
        - original_price: The original price of the deal (optional)
        - average_price: The average price of the deal (optional)
        - original_link: Original deal link (redirect to external website link)
        - deal_link: Link to deal detail page of the current website (optional)
        - description: Full description of the deal (optional)
        - categories: The categories to which the deal belongs
        - expiration: The expiration date of the deal (optional)
        - region: Region code (e.g., 'au', 'us')
        - extra_info: Extra information of the deal (optional, any valueable data that is not included in the above fields such as asin, badge, etc.)"""