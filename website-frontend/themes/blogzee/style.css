/*!
Theme Name: Blogzee
Theme URI: http://blazethemes.com/theme/blogzee-free
Author: BlazeThemes
Author URI: https://blazethemes.com/
Description: Blogzee Theme helps to illustrate your distinguished story through its interactive and stylish page layouts.  Adorned with multiple eye-catching demos, blogzee theme allows you to set up your blog in minimal steps by importing unique demos.  Not only is it beautiful, it’s immensely easy to customize too. You will find SEO options, theme settings, menu settings, mobile view options, design controls, and all in one central place - Live Customizer. After viewing the demo and customizer settings, you will soon realize the dedicated focus that Blogzee Theme has put on mobile users, SEO, and UI/UX design. As a result, it should come as no surprise that there are image Settings - image ratio, image size, image border, image box shadow for each block and widget. It has more to offer you to extend with additional features.
Version: 1.0.2
Tested up to: 6.8
Requires PHP: 5.6
License: GNU General Public License v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: blogzee
Tags: blog, entertainment, one-column, two-columns, grid-layout, left-sidebar, right-sidebar, custom-header, flexible-header, custom-background, custom-colors, custom-menu, featured-images, full-width-template, post-formats, sticky-post, rtl-language-support, footer-widgets, theme-options, threaded-comments, translation-ready

This theme, like WordPress, is licensed under the GPL.
Use it to make something cool, have fun, and share what you've learned.

Blogzee is based on Underscores https://underscores.me/, (C) 2012-2020 Automattic, Inc.
Underscores is distributed under the terms of the GNU GPL v2 or later.

Normalizing styles have been helped along thanks to the fine work of
Nicolas Gallagher and Jonathan Neal https://necolas.github.io/normalize.css/
*/

/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------
# Generic
	- Normalize
	- Box sizing
# Base
	- Typography
	- Elements
	- Links
	- Forms
## Layouts
# Components
	- Navigation
	- Posts and pages
	- Comments
	- Widgets
	- Media
	- Captions
	- Galleries
# plugins
	- Jetpack infinite scroll
# Utilities
	- Accessibility
	- Alignments

--------------------------------------------------------------*/

/*--------------------------------------------------------------
# Generic
--------------------------------------------------------------*/

/* Normalize
--------------------------------------------- */

/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */

/* Document
	 ========================================================================== */

/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */
html {
	line-height: 1.15;
	-webkit-text-size-adjust: 100%;
}

/* Sections
	 ========================================================================== */

/**
 * Remove the margin in all browsers.
 */
body {
	margin: 0;
}

/**
 * Render the `main` element consistently in IE.
 */
main {
	display: block;
}

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */
h1 {
	font-size: 2em;
	margin: 0.67em 0;
}

/* Grouping content
	 ========================================================================== */

/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */
hr {
	box-sizing: content-box;
	height: 0;
	overflow: visible;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
pre {
	font-family: monospace, monospace;
	font-size: 1em;
}

/* Text-level semantics
	 ========================================================================== */

/**
 * Remove the gray background on active links in IE 10.
 */
a {
	background-color: transparent;
}

/**
 * 1. Remove the bottom border in Chrome 57-
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */
abbr[title] {
	border-bottom: none;
	text-decoration: underline;
	text-decoration: underline dotted;
}

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */
b,
strong {
	font-weight: bolder;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
code,
kbd,
samp {
	font-family: monospace, monospace;
	font-size: 1em;
}

/**
 * Add the correct font size in all browsers.
 */
small {
	font-size: 80%;
}

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */
sub,
sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}

sub {
	bottom: -0.25em;
}

sup {
	top: -0.5em;
}

/* Embedded content
	 ========================================================================== */

/**
 * Remove the border on images inside links in IE 10.
 */
img {
	border-style: none;
}

/* Forms
	 ========================================================================== */

/**
 * 1. Change the font styles in all browsers.
 * 2. Remove the margin in Firefox and Safari.
 */
button,
input,
optgroup,
select,
textarea {
	font-family: inherit;
	font-size: 100%;
	line-height: 1.15;
	margin: 0;
}

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */
button,
input {
	overflow: visible;
}

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */
button,
select {
	text-transform: none;
}

/**
 * Correct the inability to style clickable types in iOS and Safari.
 */
button,
[type="button"],
[type="reset"],
[type="submit"] {
	-webkit-appearance: button;
}

/**
 * Remove the inner border and padding in Firefox.
 */
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
	border-style: none;
	padding: 0;
}

/**
 * Restore the focus styles unset by the previous rule.
 */
button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
	outline: 1px dotted ButtonText;
}

/**
 * Correct the padding in Firefox.
 */
fieldset {
	padding: 0.35em 0.75em 0.625em;
}

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *		`fieldset` elements in all browsers.
 */
legend {
	box-sizing: border-box;
	color: inherit;
	display: table;
	max-width: 100%;
	padding: 0;
	white-space: normal;
}

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
progress {
	vertical-align: baseline;
}

/**
 * Remove the default vertical scrollbar in IE 10+.
 */
textarea {
	overflow: auto;
}

/**
 * 1. Add the correct box sizing in IE 10.
 * 2. Remove the padding in IE 10.
 */
[type="checkbox"],
[type="radio"] {
	box-sizing: border-box;
	padding: 0;
}

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
	height: auto;
}

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */
[type="search"] {
	-webkit-appearance: textfield;
	outline-offset: -2px;
}

/**
 * Remove the inner padding in Chrome and Safari on macOS.
 */
[type="search"]::-webkit-search-decoration {
	-webkit-appearance: none;
}

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
::-webkit-file-upload-button {
	-webkit-appearance: button;
	font: inherit;
}

/* Interactive
	 ========================================================================== */

/*
 * Add the correct display in Edge, IE 10+, and Firefox.
 */
details {
	display: block;
}

/*
 * Add the correct display in all browsers.
 */
summary {
	display: list-item;
}

/* Misc
	 ========================================================================== */

/**
 * Add the correct display in IE 10+.
 */
template {
	display: none;
}

/**
 * Add the correct display in IE 10.
 */
[hidden] {
	display: none;
}

/* Box sizing
--------------------------------------------- */

/* Inherit box-sizing to more easily change it's value on a component level.
@link http://css-tricks.com/inheriting-box-sizing-probably-slightly-better-best-practice/ */
*,
*::before,
*::after {
	box-sizing: inherit;
}

html {
	box-sizing: border-box;
}

/*--------------------------------------------------------------
# Base
--------------------------------------------------------------*/

/* Typography
--------------------------------------------- */
body,
button,
input,
select,
optgroup,
textarea {
	color: #404040;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
	font-size: 1rem;
	line-height: 1.5;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	clear: both;
}

p {
	margin-bottom: 1.5em;
}

dfn,
cite,
em,
i {
	font-style: italic;
}

blockquote {
	margin: 0 1.5em;
}

address {
	margin: 0 0 1.5em;
}

pre {
	background: #eee;
	font-family: "Courier 10 Pitch", courier, monospace;
	line-height: 1.6;
	margin-bottom: 1.6em;
	max-width: 100%;
	overflow: auto;
	padding: 1.6em;
}

code,
kbd,
tt,
var {
	font-family: monaco, consolas, "Andale Mono", "DejaVu Sans Mono", monospace;
}

abbr,
acronym {
	border-bottom: 1px dotted #666;
	cursor: help;
}

mark,
ins {
	background: #fff9c0;
	text-decoration: none;
}

big {
	font-size: 125%;
}

/* Elements
--------------------------------------------- */
body {
	background: #fff;
}

hr {
	background-color: #ccc;
	border: 0;
	height: 1px;
	margin-bottom: 1.5em;
}

ul,
ol {
	margin: 0 0 1.5em 1.5em;
}

ul {
	list-style: disc;
}

ol {
	list-style: decimal;
}

li > ul,
li > ol {
	margin-bottom: 0;
	margin-left: 1.5em;
}

dt {
	font-weight: 700;
}

dd {
	margin: 0 1.5em 1.5em;
}


/* Make sure embeds and iframes fit their containers. */
embed,
iframe,
object {
	max-width: 100%;
}

img {
	height: auto;
	max-width: 100%;
}

figure {
	margin: 1em 0;
}

table {
	margin: 0 0 1.5em;
	width: 100%;
}

/* Links
--------------------------------------------- */
a {
	color: #4169e1;
}

a:visited {
	color: #800080;
}

a:hover,
a:focus,
a:active {
	color: #191970;
}

a:focus {
	outline: thin dotted;
}

a:hover,
a:active {
	outline: 0;
}

/* Forms
--------------------------------------------- */
button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
	border: 1px solid;
	border-color: #ccc #ccc #bbb;
	border-radius: 3px;
	background: #e6e6e6;
	color: rgba(0, 0, 0, 0.8);
	line-height: 1;
	padding: 0.6em 1em 0.4em;
}

button:hover,
input[type="button"]:hover,
input[type="reset"]:hover,
input[type="submit"]:hover {
	border-color: #ccc #bbb #aaa;
}

button:active,
button:focus,
input[type="button"]:active,
input[type="button"]:focus,
input[type="reset"]:active,
input[type="reset"]:focus,
input[type="submit"]:active,
input[type="submit"]:focus {
	border-color: #aaa #bbb #bbb;
}

input[type="text"],
input[type="email"],
input[type="url"],
input[type="password"],
input[type="search"],
input[type="number"],
input[type="tel"],
input[type="range"],
input[type="date"],
input[type="month"],
input[type="week"],
input[type="time"],
input[type="datetime"],
input[type="datetime-local"],
input[type="color"],
textarea {
	color: #666;
	border: 1px solid #ccc;
	border-radius: 3px;
	padding: 3px;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="password"]:focus,
input[type="search"]:focus,
input[type="number"]:focus,
input[type="tel"]:focus,
input[type="range"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="week"]:focus,
input[type="time"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="color"]:focus,
textarea:focus {
	color: #111;
}

select {
	border: 1px solid #ccc;
}

textarea {
	width: 100%;
}

/*--------------------------------------------------------------
# Layouts
--------------------------------------------------------------*/

/*--------------------------------------------------------------
# Components
--------------------------------------------------------------*/

/* Navigation
--------------------------------------------- */
.main-navigation {
	display: block;
	width: 100%;
}

.main-navigation ul {
	display: none;
	list-style: none;
	margin: 0;
	padding-left: 0;
}

.main-navigation ul ul {
	box-shadow: 0 3px 3px rgba(0, 0, 0, 0.2);
	float: left;
	position: absolute;
	top: 100%;
	left: -999em;
	z-index: 99999;
}

.main-navigation ul ul ul {
	left: -999em;
	top: 0;
}

.main-navigation ul ul a {
	width: 200px;
}

.main-navigation li {
	position: relative;
}

.main-navigation a {
	display: block;
	text-decoration: none;
}

/* Small menu. */
.menu-toggle,
.main-navigation.toggled ul {
	display: block;
}

.main-navigation.toggled ul li + li {
	border-top: 1px solid #0000000d;
}

@media screen and (min-width: 48.5em) {
	.menu-toggle {
		display: none;
	}

	.main-navigation ul {
		display: flex;
		flex-wrap: wrap;
	}

	.main-navigation ul li:hover > ul,
	.main-navigation ul li.focus > ul {
		left: auto;
	}

	.main-navigation ul ul li:hover > ul,
	.main-navigation ul ul li.focus > ul {
		display: block;
		left: auto;
	}
}

.site-main .comment-navigation,
.site-main
.posts-navigation,
.site-main
.post-navigation {
	margin: 0 0 1.5em;
}

.comment-navigation .nav-links,
.posts-navigation .nav-links,
.post-navigation .nav-links {
	display: flex;
	justify-content: space-between;
	-webkit-justify-content: space-between;
}

/* Posts and pages
--------------------------------------------- */
.sticky {
	display: block;
}

.post,
.page {
	margin: 0 0 1.5em;
}

.updated:not(.published) {
	display: none;
}

.page-content,
.entry-content,
.entry-summary {
	margin: 1.5em 0 0;
}

.page-links {
	clear: both;
	margin: 0 0 1.5em;
}

/* Comments
--------------------------------------------- */
.comment-content a {
	word-wrap: break-word;
}

.bypostauthor {
	display: block;
}

/* Widgets
--------------------------------------------- */
.widget {
	margin: 0 0 1.5em;
}

.widget select {
	max-width: 100%;
}

/* Media
--------------------------------------------- */
.page-content .wp-smiley,
.entry-content .wp-smiley,
.comment-content .wp-smiley {
	border: none;
	margin-bottom: 0;
	margin-top: 0;
	padding: 0;
}

/* Make sure logo link wraps around logo image. */
.custom-logo-link {
	display: inline-block;
}

/* Captions
--------------------------------------------- */
.wp-caption {
	margin-bottom: 1.5em;
	max-width: 100%;
}

.wp-caption img[class*="wp-image-"] {
	display: block;
	margin-left: auto;
	margin-right: auto;
}

.wp-caption .wp-caption-text {
	margin: 0.8075em 0;
}

.wp-caption-text {
	text-align: center;
}

/* Galleries
--------------------------------------------- */
.gallery {
	margin-bottom: 1.5em;
	display: grid;
	grid-gap: 1.5em;
}

.gallery-item {
	display: inline-block;
	text-align: center;
	width: 100%;
}

.gallery-columns-2 {
	grid-template-columns: repeat(2, 1fr);
}

.gallery-columns-3 {
	grid-template-columns: repeat(3, 1fr);
}

.gallery-columns-4 {
	grid-template-columns: repeat(4, 1fr);
}

.gallery-columns-5 {
	grid-template-columns: repeat(5, 1fr);
}

.gallery-columns-6 {
	grid-template-columns: repeat(6, 1fr);
}

.gallery-columns-7 {
	grid-template-columns: repeat(7, 1fr);
}

.gallery-columns-8 {
	grid-template-columns: repeat(8, 1fr);
}

.gallery-columns-9 {
	grid-template-columns: repeat(9, 1fr);
}

.gallery-caption {
	display: block;
}

/*--------------------------------------------------------------
# Plugins
--------------------------------------------------------------*/

/* Jetpack infinite scroll
--------------------------------------------- */

/* Hide the Posts Navigation and the Footer when Infinite Scroll is in use. */
.infinite-scroll .posts-navigation,
.infinite-scroll.neverending .site-footer {
	display: none;
}

/* Re-display the Theme Footer when Infinite Scroll has reached its end. */
.infinity-end.neverending .site-footer {
	display: block;
}

/*--------------------------------------------------------------
# Utilities
--------------------------------------------------------------*/

/* Accessibility
--------------------------------------------- */

/* Text meant only for screen readers. */
.screen-reader-text {
	border: 0;
	clip: rect(1px, 1px, 1px, 1px);
	clip-path: inset(50%);
	height: 1px;
	margin: -1px;
	overflow: hidden;
	padding: 0;
	position: absolute !important;
	width: 1px;
	word-wrap: normal !important;
}

.screen-reader-text:focus {
	background-color: #f1f1f1;
	border-radius: 3px;
	box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
	clip: auto !important;
	clip-path: none;
	color: #21759b;
	display: block;
	font-size: 0.875rem;
	font-weight: 700;
	height: auto;
	left: 5px;
	line-height: normal;
	padding: 15px 23px 14px;
	text-decoration: none;
	top: 5px;
	width: auto;
	z-index: 100000;
}

/* Do not show the outline on the skip link target. */
#primary[tabindex="-1"]:focus {
	outline: 0;
}

/* Alignments
--------------------------------------------- */
.alignleft {

	/*rtl:ignore*/
	float: left;

	/*rtl:ignore*/
	margin-right: 1.5em;
	margin-bottom: 1.5em;
}

.alignright {

	/*rtl:ignore*/
	float: right;

	/*rtl:ignore*/
	margin-left: 1.5em;
	margin-bottom: 1.5em;
}

.aligncenter {
	clear: both;
	display: block;
	margin-left: auto;
	margin-right: auto;
	margin-bottom: 1.5em;
}

/*--------------------------------------------------------------
# Post Voting System
--------------------------------------------------------------*/
.blogzee-post-voting {
	margin: 2rem 0;
	padding: 1.5rem;
	background: #f8f9fa;
	border-radius: 8px;
	border: 1px solid #e9ecef;
}

.voting-buttons {
	display: flex;
	gap: 1rem;
	justify-content: center;
	align-items: center;
}

.vote-btn {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	padding: 0.75rem 1.25rem;
	background: #ffffff;
	border: 2px solid #dee2e6;
	border-radius: 25px;
	color: #6c757d;
	font-size: 0.9rem;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
	text-decoration: none;
	min-width: 100px;
	justify-content: center;
}

/* Hover effects for non-touch devices */
@media (hover: hover) and (pointer: fine) {
	.vote-btn:hover {
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		text-decoration: none;
	}
}

/* Touch-friendly active states for mobile */
@media (hover: none) and (pointer: coarse) {
	.vote-btn:active {
		transform: scale(0.95);
		transition: transform 0.1s ease;
	}
}

/* Touch device specific styles */
.touch-device .vote-btn {
	-webkit-tap-highlight-color: transparent;
	user-select: none;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
}

.touch-device .vote-btn:active {
	background-color: rgba(0, 0, 0, 0.05);
}

.vote-btn:focus {
	outline: none;
	box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.vote-btn:disabled {
	opacity: 0.6;
	cursor: not-allowed;
	transform: none;
}

.vote-btn.loading {
	position: relative;
	color: transparent;
}

.vote-btn.loading::after {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 16px;
	height: 16px;
	border: 2px solid #f3f3f3;
	border-top: 2px solid #007cba;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: translate(-50%, -50%) rotate(0deg); }
	100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.like-btn {
	border-color: #28a745;
	color: #28a745;
}

.like-btn:hover {
	background: #28a745;
	color: white;
}

.like-btn.active {
	background: #28a745;
	color: white;
	border-color: #28a745;
}

.dislike-btn {
	border-color: #dc3545;
	color: #dc3545;
}

.dislike-btn:hover {
	background: #dc3545;
	color: white;
}

.dislike-btn.active {
	background: #dc3545;
	color: white;
	border-color: #dc3545;
}

.vote-count {
	font-weight: 600;
	min-width: 20px;
	text-align: center;
}

.login-message {
	margin-top: 1rem;
	padding: 0.75rem 1rem;
	background: #fff3cd;
	border: 1px solid #ffeaa7;
	border-radius: 4px;
	color: #856404;
	text-align: center;
	font-size: 0.9rem;
}

.vote-feedback,
.vote-error {
	position: absolute;
	top: -50px;
	left: 50%;
	transform: translateX(-50%);
	padding: 0.75rem 1.25rem;
	border-radius: 6px;
	font-size: 0.9rem;
	font-weight: 500;
	white-space: nowrap;
	z-index: 1000;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
	display: none; /* Initially hidden */
}

.vote-feedback {
	background: #d4edda;
	color: #155724;
	border: 1px solid #c3e6cb;
}

.vote-error {
	background: #f8d7da;
	color: #721c24;
	border: 1px solid #f5c6cb;
}

/* Enhanced Mobile Responsiveness */
/* Tablet and small desktop */
@media (max-width: 768px) {
	.blogzee-post-voting {
		margin: 1.5rem 0;
		padding: 1rem;
	}

	.voting-buttons {
		flex-direction: row;
		gap: 0.75rem;
		justify-content: center;
	}

	.vote-btn {
		flex: 1;
		max-width: 150px;
		padding: 0.75rem 1rem;
		font-size: 0.9rem;
		min-height: 44px; /* Touch-friendly minimum size */
	}

	.vote-btn i {
		font-size: 1rem;
	}
}

/* Mobile landscape and small tablets */
@media (max-width: 480px) {
	.blogzee-post-voting {
		margin: 1rem 0;
		padding: 0.75rem;
	}

	.voting-buttons {
		gap: 0.5rem;
	}

	.vote-btn {
		padding: 0.75rem 0.75rem;
		font-size: 0.85rem;
		min-width: 100px;
		min-height: 44px; /* Maintain touch-friendly size */
	}

	.vote-text {
		display: none; /* Hide text on small screens, keep icons and counts */
	}

	.vote-count {
		font-size: 0.9rem;
		font-weight: 700;
	}
}

/* Very small mobile screens */
@media (max-width: 375px) {
	.blogzee-post-voting {
		padding: 0.5rem;
	}

	.voting-buttons {
		flex-direction: column;
		gap: 0.5rem;
	}

	.vote-btn {
		width: 100%;
		max-width: none;
		padding: 0.75rem;
		justify-content: center;
	}
}

/* Extra small screens */
@media (max-width: 320px) {
	.vote-btn {
		padding: 0.6rem;
		font-size: 0.8rem;
		min-height: 40px;
	}

	.vote-btn i {
		font-size: 0.9rem;
	}

	.vote-count {
		font-size: 0.85rem;
	}
}

/*--------------------------------------------------------------
# Post Like Rate Display
--------------------------------------------------------------*/
.post-like-rate {
	display: inline-flex;
	align-items: center;
	gap: 0.5rem;
	margin-left: 1rem;
	font-size: 0.85rem;
}

.like-rate-indicator {
	display: inline-flex;
	align-items: center;
	gap: 0.25rem;
	padding: 0.25rem 0.5rem;
	border-radius: 12px;
	font-weight: 500;
	font-size: 0.8rem;
}

.like-rate-indicator i {
	font-size: 0.75rem;
}

.rate-excellent {
	background: #d4edda;
	color: #155724;
	border: 1px solid #c3e6cb;
}

.rate-good {
	background: #d1ecf1;
	color: #0c5460;
	border: 1px solid #bee5eb;
}

.rate-neutral {
	background: #fff3cd;
	color: #856404;
	border: 1px solid #ffeaa7;
}

.rate-poor {
	background: #f8d7da;
	color: #721c24;
	border: 1px solid #f5c6cb;
}

.rate-text {
	color: #6c757d;
	font-size: 0.75rem;
}

/* Archive layout specific styles */
.post-meta .post-like-rate {
	margin-left: 0.75rem;
}

.post-meta .post-like-rate .like-rate-indicator {
	padding: 0.2rem 0.4rem;
	font-size: 0.75rem;
}

.post-meta .post-like-rate .rate-text {
	font-size: 0.7rem;
}

/* Entry meta specific styles */
.entry-meta .post-like-rate {
	margin-left: 0.75rem;
	margin-top: 0.25rem;
}

.entry-meta .post-like-rate .like-rate-indicator {
	padding: 0.2rem 0.4rem;
	font-size: 0.75rem;
}

/* Enhanced responsive design for like rate */
@media (max-width: 768px) {
	.post-like-rate {
		margin-left: 0.5rem;
		gap: 0.25rem;
	}

	.like-rate-indicator {
		padding: 0.2rem 0.4rem;
		font-size: 0.75rem;
		min-height: 24px; /* Ensure touch-friendly size */
	}

	.rate-text {
		font-size: 0.7rem;
	}

	/* Archive layout adjustments */
	.post-meta .post-like-rate {
		margin-left: 0.5rem;
		margin-top: 0.25rem;
	}

	.entry-meta .post-like-rate {
		margin-left: 0.5rem;
		margin-top: 0.5rem;
		display: block; /* Stack on new line for better readability */
	}
}

@media (max-width: 480px) {
	.post-like-rate {
		margin-left: 0.25rem;
	}

	.post-like-rate .rate-text {
		display: none; /* Hide vote count text on small screens */
	}

	.like-rate-indicator {
		padding: 0.15rem 0.35rem;
		font-size: 0.7rem;
		min-height: 22px;
	}

	.like-rate-indicator i {
		font-size: 0.65rem;
	}

	/* Better mobile layout for post meta */
	.entry-meta .post-like-rate {
		margin-left: 0;
		margin-top: 0.5rem;
	}
}

@media (max-width: 375px) {
	.like-rate-indicator {
		padding: 0.1rem 0.3rem;
		font-size: 0.65rem;
		min-height: 20px;
	}

	.rate-percentage {
		font-size: 0.65rem;
	}
}

@media (max-width: 320px) {
	.post-like-rate {
		margin-left: 0;
		margin-top: 0.25rem;
	}

	.like-rate-indicator {
		padding: 0.1rem 0.25rem;
		font-size: 0.6rem;
		min-height: 18px;
	}

	.like-rate-indicator i {
		font-size: 0.6rem;
	}
}
