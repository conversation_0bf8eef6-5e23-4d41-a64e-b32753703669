!function(e,i){const{ajaxUrl:t,custom_callback:n,_wpnonce:o,custom:s}=customizerExtrasObject;i.each(s,(function(i,t){"single_section_panel"==i?e.panel(i,(function(i){i.expanded.bind((function(i){i&&e.previewer.previewUrl.set(t)}))})):e.section(i,(function(i){i.expanded.bind((function(n){const{id:o}=i;"archive_general_section"===o&&e.previewer.previewUrl()===t||n&&e.previewer.previewUrl.set(t)}))}))}));i.each(n,(function(t,n){wp.customize(t,(function(t){t.bind((function(t){i.each(n,(function(n,o){JSON.stringify(t)==n?i.each(o,(function(i,t){e.control(t).activate()})):i.each(o,(function(i,t){e.control(t).deactivate()}))})),t in n&&i.each(n[t],(function(i,t){e.control(t).activate()}))}))}))})),i(document).on("click",".customize-info-box-action-control .info-box-button",(function(){var e=i(this),n=e.data("action"),s=e.html();i.ajax({method:"post",url:t,data:{action:n,_wpnonce:o},beforeSend:function(){e.html("Processing"),e.attr("disabled",!0)},success:function(){e.html(s)},complete:function(){window.location.reload()}})}));const c={init:function(){this.headerBuilder(),this.footerBuilder(),this.addActiveClasses()},widgetSections:{},headerBuilderId:"",footerBuilderId:"",headerBuilder:function(){this.headerBuilderId="header_builder",this.widgetSections.header=this.builderBehaviour(this.headerBuilderId)},footerBuilder:function(){this.footerBuilderId="footer_builder",this.widgetSections.footer=this.builderBehaviour(this.footerBuilderId)},builderBehaviour:function(i){let t=e.control(i);const{widgets:n,builder_settings_section:o}=t.params;return[...this.getWidgetSections(n),o]},getWidgetSections:function(e){return Object.values(e).reduce(((e,i)=>{const{section:t}=i;return e=[...e,t]}),[])},addActiveClasses:function(){const t=this.widgetSections,{header:n,footer:o}=t,s=this.headerBuilderId,c=this.footerBuilderId;e.section.each(((t,r)=>{t.expanded.bind((function(d){if(d)if(t.contentContainer.hasClass("blogzee-builder-related")){let i="";if(n.includes(r)&&(i=s),o.includes(r)&&(i=c),""!==i){let t=e.control(i);const{builder_settings_section:n}=t.params,{container:o}=t,s=e.section(n).contentContainer;o.parent().addClass("is-active").siblings().removeClass("is-active"),s.addClass("active-builder-setting").siblings().removeClass("active-builder-setting"),s.parents("#customize-controls").siblings("#customize-preview").addClass("blogzee-builder--on")}}else i(".blogzee-builder.is-active")&&i(".blogzee-builder.is-active").removeClass("is-active"),i(".blogzee-builder-related.active-builder-setting")&&i(".blogzee-builder-related.active-builder-setting").removeClass("active-builder-setting"),i("#customize-preview").removeClass("blogzee-builder--on")}))}))}};i(document).ready((function(){c.init()}))}(wp.customize,jQuery);