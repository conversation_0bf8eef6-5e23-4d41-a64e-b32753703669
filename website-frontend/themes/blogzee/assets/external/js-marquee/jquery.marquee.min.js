(function(e){"use strict";if(typeof define==="function"&&define.amd){define(["jquery"],e)}else if(typeof exports!=="undefined"){module.exports=e(require("jquery"))}else{e(jQuery)}})(function(X){X.fn.marquee=function(B){return this.each(function(){var i=X.extend({},X.fn.marquee.defaults,B),a=X(this),r,n,s,o,u,f=3,e="animation-play-state",d=false,l=function(e,t,i){var a=["webkit","moz","MS","o",""];for(var r=0;r<a.length;r++){if(!a[r])t=t.toLowerCase();e.addEventListener(a[r]+t,i,false)}},p=function(e){var t=[];for(var i in e){if(e.hasOwnProperty(i)){t.push(i+":"+e[i])}}t.push();return"{"+t.join(",")+"}"},c=function(){a.timer=setTimeout(A,i.delayBeforeStart)},t={pause:function(){if(d&&i.allowCss3Support){r.css(e,"paused")}else{if(X.fn.pause){r.pause()}}a.data("runningStatus","paused");a.trigger("paused")},resume:function(){if(d&&i.allowCss3Support){r.css(e,"running")}else{if(X.fn.resume){r.resume()}}a.data("runningStatus","resumed");a.trigger("resumed")},toggle:function(){t[a.data("runningStatus")==="resumed"?"pause":"resume"]()},destroy:function(){clearTimeout(a.timer);a.find("*").addBack().off();a.html(a.find(".js-marquee:first").html())}};if(typeof B==="string"){if(X.isFunction(t[B])){if(!r){r=a.find(".js-marquee-wrapper")}if(a.data("css3AnimationIsSupported")===true){d=true}t[B]()}return}var m={},g;X.each(i,function(e){g=a.attr("data-"+e);if(typeof g!=="undefined"){switch(g){case"true":g=true;break;case"false":g=false;break}i[e]=g}});if(i.speed){i.duration=parseInt(a.width(),10)/i.speed*1e3}o=i.direction==="up"||i.direction==="down";i.gap=i.duplicated?parseInt(i.gap):0;a.wrapInner('<div class="js-marquee"></div>');var h=a.find(".js-marquee").css({"margin-right":i.gap,float:"left"});if(i.duplicated){if(i.duplicateCount<=0){i.duplicateCount=1}for(let e=0;e<i.duplicateCount;e++){h.clone(true).appendTo(a)}}a.wrapInner('<div style="width:100000px" class="js-marquee-wrapper"></div>');r=a.find(".js-marquee-wrapper");if(o){var y=a.height();r.removeAttr("style");a.height(y);a.find(".js-marquee").css({float:"none","margin-bottom":i.gap,"margin-right":0});if(i.duplicated){a.find(".js-marquee:last").css({"margin-bottom":0})}var v=a.find(".js-marquee:first").height()+i.gap;if(i.startVisible&&!i.duplicated){i._completeDuration=(parseInt(v,10)+parseInt(y,10))/parseInt(y,10)*i.duration;i.duration=parseInt(v,10)/parseInt(y,10)*i.duration}else{i.duration=(parseInt(v,10)+parseInt(y,10))/parseInt(y,10)*i.duration}}else{u=a.find(".js-marquee:first").width()+i.gap;n=a.width();if(i.startVisible&&!i.duplicated){i._completeDuration=(parseInt(u,10)+parseInt(n,10))/parseInt(n,10)*i.duration;i.duration=parseInt(u,10)/parseInt(n,10)*i.duration}else{i.duration=(parseInt(u,10)+parseInt(n,10))/parseInt(n,10)*i.duration}}if(i.duplicated){i.duration=i.duration/2}if(i.allowCss3Support){var x=document.body||document.createElement("div"),I="marqueeAnimation-"+Math.floor(Math.random()*1e7),w="Webkit Moz O ms Khtml".split(" "),S="animation",b="",q="";if(x.style.animation!==undefined){q="@keyframes "+I+" ";d=true}if(d===false){for(var j=0;j<w.length;j++){if(x.style[w[j]+"AnimationName"]!==undefined){var C="-"+w[j].toLowerCase()+"-";S=C+S;e=C+e;q="@"+C+"keyframes "+I+" ";d=true;break}}}if(d){b=I+" "+i.duration/1e3+"s "+i.delayBeforeStart/1e3+"s infinite "+i.css3easing;a.data("css3AnimationIsSupported",true)}}var V=function(){r.css("transform","translateY("+(i.direction==="up"?y+"px":"-"+v+"px")+")")},k=function(){r.css("transform","translateX("+(i.direction==="left"?n+"px":"-"+u+"px")+")")};if(i.duplicated){if(o){if(i.startVisible){r.css("transform","translateY(0)")}else{r.css("transform","translateY("+(i.direction==="up"?y+"px":"-"+(v*2-i.gap)+"px")+")")}}else{if(i.startVisible){r.css("transform","translateX(0)")}else{r.css("transform","translateX("+(i.direction==="left"?n+"px":"-"+(u*2-i.gap)+"px")+")")}}if(!i.startVisible){f=1}}else if(i.startVisible){f=2}else{if(o){V()}else{k()}}var A=function(){if(i.duplicated){if(f===1){i._originalDuration=i.duration;if(o){i.duration=i.direction==="up"?i.duration+y/(v/i.duration):i.duration*2}else{i.duration=i.direction==="left"?i.duration+n/(u/i.duration):i.duration*2}if(b){b=I+" "+i.duration/1e3+"s "+i.delayBeforeStart/1e3+"s "+i.css3easing}f++}else if(f===2){i.duration=i._originalDuration;if(b){I=I+"0";q=X.trim(q)+"0 ";b=I+" "+i.duration/1e3+"s 0s infinite "+i.css3easing}f++}}if(o){if(i.duplicated){if(f>2){r.css("transform","translateY("+(i.direction==="up"?0:"-"+v+"px")+")")}s={transform:"translateY("+(i.direction==="up"?"-"+v+"px":0)+")"}}else if(i.startVisible){if(f===2){if(b){b=I+" "+i.duration/1e3+"s "+i.delayBeforeStart/1e3+"s "+i.css3easing}s={transform:"translateY("+(i.direction==="up"?"-"+v+"px":y+"px")+")"};f++}else if(f===3){i.duration=i._completeDuration;if(b){I=I+"0";q=X.trim(q)+"0 ";b=I+" "+i.duration/1e3+"s 0s infinite "+i.css3easing}V()}}else{V();s={transform:"translateY("+(i.direction==="up"?"-"+r.height()+"px":y+"px")+")"}}}else{if(i.duplicated){if(f>2){r.css("transform","translateX("+(i.direction==="left"?0:"-"+u+"px")+")")}s={transform:"translateX("+(i.direction==="left"?"-"+u+"px":0)+")"}}else if(i.startVisible){if(f===2){if(b){b=I+" "+i.duration/1e3+"s "+i.delayBeforeStart/1e3+"s "+i.css3easing}s={transform:"translateX("+(i.direction==="left"?"-"+u+"px":n+"px")+")"};f++}else if(f===3){i.duration=i._completeDuration;if(b){I=I+"0";q=X.trim(q)+"0 ";b=I+" "+i.duration/1e3+"s 0s infinite "+i.css3easing}k()}}else{k();s={transform:"translateX("+(i.direction==="left"?"-"+u+"px":n+"px")+")"}}}a.trigger("beforeStarting");if(d){r.css(S,b);var e=q+" { 100%  "+p(s)+"}",t=r.find("style");if(t.length!==0){t.filter(":last").html(e)}else{X("head").append("<style>"+e+"</style>")}l(r[0],"AnimationIteration",function(){a.trigger("finished")});l(r[0],"AnimationEnd",function(){A();a.trigger("finished")})}else{r.animate(s,i.duration,i.easing,function(){a.trigger("finished");if(i.pauseOnCycle){c()}else{A()}})}a.data("runningStatus","resumed")};a.on("pause",t.pause);a.on("resume",t.resume);if(i.pauseOnHover){a.on("mouseenter",t.pause);a.on("mouseleave",t.resume)}if(d&&i.allowCss3Support){A()}else{c()}})};X.fn.marquee.defaults={allowCss3Support:true,css3easing:"linear",easing:"linear",delayBeforeStart:1e3,direction:"left",duplicated:false,duplicateCount:1,duration:5e3,speed:0,gap:20,pauseOnCycle:false,pauseOnHover:false,startVisible:false}});
